import { Event } from '~types/api/event';
import {PaginatedResponse} from '../../types/api';

export enum BUTTON_STATUS_ENUM {
  LOADING = 'loading',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SENT = 'sent',
  EMPTY = 'empty',
}

export enum CHAT_MESSAGE_TYPE_ENUM {
  INFO = 'info',
  MESSAGE = 'message',
  ISSUE_CONFIRMATION = 'issue_confirmation',
  EVENT = 'event',
}

export enum CHAT_TYPE_ENUM {
  PRIVATE = 'private',
  GROUP = 'group',
  ORGANISATION = 'organisation',
  CONTACT_PYXI = 'contact-pyxi'
}

export type ChatTypeEnum = CHAT_TYPE_ENUM | `${CHAT_TYPE_ENUM}`;
export type ConciergeTypeEnum = CONCIERGE_CHAT_TYPE_ENUM | `${CONCIERGE_CHAT_TYPE_ENUM}`;

export enum CONCIERGE_CHAT_TYPE_ENUM {
  ONE_ON_ONE = 'one-on-one',
  BROADCAST = 'broadcast',
}

export type ChatMessageType = {
  message_id: string,
  message: string;
  sender: string;
  sender_image: string;
  sender_id: string;
  timestamp: string;
  type: CHAT_MESSAGE_TYPE_ENUM;
  readUserIds: string[];
  event?: any;
};

export type ChatType = {
  eventName?: string;
  history: ChatMessageType[];
  type: ChatTypeEnum;
  userIds: string[];
  users: string[];
  eventId?: number;
  eventImage?: string;
  status?: string;
  progress: string,
  issueNumber?: number[];
  event?: Event | null,
};

export type ChatTypeWithKey = ChatType & {key: string};

export type ConciergeChatType = {
  type: ConciergeTypeEnum;
  userId: string;
  userName: string;
  messages: ConciergeChatMessageType[];
};

export type ConciergeChatTypeWithKey = ConciergeChatType & {key: string};

export type ConciergeChatMessageType = {
  status: BUTTON_STATUS_ENUM;
  timestamp: string;
  senderId: string;
  sender: string;
  requestorName?: string;
  requestorId?: string;
  requestor_image?: string;
  readUserIds: string[];
  message: string;
};

export type CreateChatPayload = {
  user_ids: string[];
};

export enum UPDATE_CHAT_STATUS {
  ACCEPTED = 'accepted',
  PENDING = 'pending',
  DECLINED = 'declined',
}

export type UpdateChatStatusPayload = {
  chatId: number;
  userId: string;
  status: UPDATE_CHAT_STATUS;
};

export type Chat = {
  chat_id: number;
  created_at: string;
  last_message: {
    message_id: string;
    text: string;
    created_at: string;
    user_id: string;
    chat_id: number;
  };
  users: ChatUser[];
};

export type ChatUser = {
  uid: string;
  photo: string | null;
  first_name: string;
  last_name: string | null;
};

export type GetChatsListPayload = {
  user_id: string;
  limit: number;
  offset: number;
};

export type Message = {
  message_id: string;
  text: string;
  created_at: string;
  chat_id: number;
  user: ChatUser;
};

export type ChatState = {
  chatId: number;
  offset: number;
  messages: Message[];
};

export enum USER_CHAT_STATUS {
  ACCEPTED = 'accepted',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

export type ChatItem = {
  chat_id: number;
  created_at: string;
  last_message: Message;
  users: {
    unread_count: number;
    status: USER_CHAT_STATUS;
    user: ChatUser;
  }[];
};

export type GetChatsResponse = PaginatedResponse<ChatItem>;

export type ChatMessagesPayload = {
  chat_id: number;
  limit: number;
  offset: number;
};

export type GetChatMessages = PaginatedResponse<Message>;

export enum WEBSOCKET_TYPE {
  CHAT = 'chat',
  MESSAGE = 'message',
  ERROR = 'error',
}

export enum WEBSOCKET_MESSAGE_ACTION {
  CREATE = 'create',
  DELETE = 'delete',
}

export type ChatContextType = {
  sendMessage: (props: {message: string; chatId: number}) => void;
  deleteMessage: (props: {messageId: string}) => void;
  enterChat: (chatId: number) => void;
  leaveChat: (chatId: number) => void;
};

export interface ChatProviderProps {
  children: React.ReactNode;
}

export type WebsocketCreateMessageResponse = {
  action: WEBSOCKET_MESSAGE_ACTION;
  data: {
    message_id: string;
    text: string;
    created_at: string;
    user_id: string;
    chat_id: number;
    user: {
      uid: string;
      photo: string | null;
      first_name: string;
      last_name: string | null;
    };
  };
  type: WEBSOCKET_TYPE;
};

export type WebsocketDeleteMessageResponse = {
  type: WEBSOCKET_TYPE.MESSAGE;
  action: WEBSOCKET_MESSAGE_ACTION.DELETE;
  data: {
    message_id: string;
    chat_id: number;
  };
};

export type WebsocketErrorMessageResponse = {
  type: WEBSOCKET_TYPE.ERROR;
  details: string;
};

type WebsocketRequest<T> = {
  type: WEBSOCKET_TYPE;
  message: T;
};

type MessageCreateRequest = {
  action: WEBSOCKET_MESSAGE_ACTION.CREATE;
  data: {
    text: string;
    chat_id: number;
  };
};

type MessageDeleteRequest = {
  action: WEBSOCKET_MESSAGE_ACTION.DELETE;
  data: {
    message_id: string;
  };
};

export enum ACTIVE_CHAT_REQUEST_ACTIONS {
  JOIN = 'join',
  LEAVE = 'leave',
}

type ActiveChatRequest = {
  action: ACTIVE_CHAT_REQUEST_ACTIONS;
  data: {
    chat_id: number;
  };
};

export type WebsocketMessageCreateRequest = WebsocketRequest<MessageCreateRequest>;

export type WebsocketMessageDeleteRequest = WebsocketRequest<MessageDeleteRequest>;

export type WebsocketActiveChatRequest = WebsocketRequest<ActiveChatRequest>;
