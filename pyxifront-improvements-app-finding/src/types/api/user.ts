export interface Coords {
  lat: number;
  long: number;
}

export enum Gender {
  MAN = 'man',
  WOMAN = 'woman',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer',
}

export interface Question {
  question: string;
  answer: string;
}
export interface User {
  uid: string;
  photo: string;
  first_name: string;
  last_name: string;
  is_registration_finished: boolean;
  gender: string;
  email: string;
  description: string;
  date_of_birth: string;
  coords: Coords;
  coords_real: Coords | null;
  groups: number[];
  subcategories: number[];
  children: {child_age: number}[];
  postal_code: string | null;
  onboarding_answers: Question[];
}

export enum USER_EVENT_STATUS {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  JOIN = 'Join',
  WATING = 'waiting',
}

export interface ValidationError {
  loc: string[];
  msg: string;
  type: string;
}

export interface IUserGroup {
  group_id: number;
  group_name: string;
}

export enum SUBSCRIPTION_STATUS {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
}

export type GetUserGroupsResponse = IUserGroup[];

export interface AddUserGroupsRequest {
  group_id: number;
}

export interface AddUserGroupsResponse {
  addedGroups: string[];
}

export interface RemoveUserGroupRequest {
  group_id: number;
}

export interface RemoveUserGroupResponse {
  removalDetails: string;
}

export interface AddUserChildRequest {
  child_age: number;
}

export interface AddUserChildResponse {
  addedChild: string;
}

export interface RemoveUserChildRequest {
  child_id: number;
}

export interface RemoveUserChildResponse {
  removalDetails: string;
}

export type GetUserSubcategoriesResponse = {
  subcategory_id: number;
  subcategory_name: string;
  user_id: string;
}[];

export interface IChild {
  child_age: number;
  child_id: number;
  user_id: string;
}

export interface RemoveUserSubcategoryRequest {
  user_id: string;
  subcategory_id: number[];
}

export interface RemoveUserSubcategoryResponse {
  confirmation: string;
}

export interface AddUserSubcategoriesRequest {
  user_id: string;
  subcategories: number[];
}

export interface AddUserSubcategoriesResponse {
  confirmation: string;
}

export interface UpdateUserCoordsRequest {
  coords_real: {
    latitude: number | null;
    longitude: number | null;
  };
}

export type UserType = 'business' | 'personal';
