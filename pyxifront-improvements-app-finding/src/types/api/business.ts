import {Coords, Question} from './user';

export interface Business {
  uid: string;
  photo: string;
  name: string;
  coords: Coords;
  is_registration_finished: boolean;
  email: string;
  description: string;
  created_at: '2023-11-21T17:05:54.582Z';
  updated_at: '2023-11-21T17:05:54.582Z';
  business_type_id: number;
  target_audiences: number[];
  domain?: string;
  user_pooling?: boolean;
  onboarding_answers: Question[];
}
