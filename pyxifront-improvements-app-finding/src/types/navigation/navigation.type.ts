import SCREENS from '~constants/screens';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Event, Ticket} from '~types/api/event';
import {ConciergeChatTypeWithKey} from '~types/chat';
import { Coords } from '~types/api/user';
import { TicketType } from '~containers/Event/BuyTicket/BuyTicket';

export type RootStackParamsList = {
  [SCREENS.ONBOARDING]: undefined;
  [SCREENS.CHANGE_PROFILE_INFO]: undefined;
  [SCREENS.CHANGE_PROFILE_INFO_BUSINESS]: undefined;
  [SCREENS.PASSWORD_FOR_DELETE_ACCOUNT]: undefined;
  [SCREENS.DELETE_LOADING]: undefined;
  [SCREENS.LOGIN]: undefined;
  [SCREENS.SIGN_UP]: undefined;
  [SCREENS.FORGOT_PASSWORD]: undefined;
  [SCREENS.HELP_CENTER]: undefined;
  [SCREENS.LOGOUT]: undefined;
  [SCREENS.NOTIFICATIONS]: undefined;
  [SCREENS.CHANGE_PASSWORD]: undefined;
  [SCREENS.PERSONAL_INFO]: {
    user: {tag: string; source: string; name: string; description: string; user_id: string, eventName?: string, coords?: Coords};
  };
  [SCREENS.SETTINGS]: undefined;
  [SCREENS.CONCIERGE_CHAT]: {chat: ConciergeChatTypeWithKey};
  [SCREENS.USER_CHAT]: {chatId: any, image?: string, userName: string};
  [SCREENS.SINGLE_CHAT]: undefined;
  [SCREENS.CHAT_INFO]: undefined;
  [SCREENS.EVENTS]: undefined;
  [SCREENS.MY_EVENT]: undefined;
  [SCREENS.EDIT_EVENT]: {
    item: Event;
  };
  [SCREENS.EDIT_SUBCATEGORY]: undefined;
  [SCREENS.CREATE_EVENT_TEMPLATE]: undefined | {
    item: Event;
  };
  [SCREENS.CREATE_EVENT_INFO]: undefined | {
    item: Event;
  };
  [SCREENS.CREATE_EVENT_GROUPS]: undefined| {
    item: Event;
  };
  [SCREENS.CREATE_EVENT_SUBMITTING]: undefined| {
    item: Event;
  };
  [SCREENS.CALENDAR]: undefined;
  [SCREENS.CALENDAR_CHANGE_DATE]: undefined;
  [SCREENS.CALENDAR_EVENT]: undefined;
  [SCREENS.HOME]: undefined;
  [SCREENS.HOME_EVENT]: {
    eventId: number;
    tag?: string;
    statusTag?: string;
    item?: Event;
  };
  [SCREENS.MAP]: undefined;
  [SCREENS.ONBOARDING_ACCOUNT_TYPE]: undefined;
  [SCREENS.ONBOARDING_GROUP]: {isBusiness?: boolean} | undefined;
  [SCREENS.ONBOARDING_CHILDREN]: undefined;
  [SCREENS.ONBOARDING_PERSONAL_INFO]: undefined;
  [SCREENS.ONBOARDING_SUBCATEGORIES]?: {isBusiness?: boolean} | undefined;
  [SCREENS.ONBOARDING_BUSINESS_INFO]: undefined;
  [SCREENS.CHATS]: undefined;
  [SCREENS.PENDING_ATTENDEES]: {eventId: number; eventName: string};
  [SCREENS.CHAT_STACK]: {key?: any; chatType?: string};
  [SCREENS.EDIT_SUBCATEGORIES]: undefined;
  [SCREENS.EDIT_PREFERANCE]: {setting?: boolean};
  [SCREENS.SETTINGS_STACK]: {
    refineSubcategories?: boolean;
    screen?: any;
    params?: any
  };
  [SCREENS.HOME_STACK]: {eventId?: number};
  [SCREENS.BUY_TICKET]: {eventId: number;};
  [SCREENS.PAYMENT_SUCCESS]: {eventId?: number; eventData?: Event; selectedTickets?: Ticket[]; order_id: string};
  [SCREENS.PURCHASE_HISTORY]: undefined;
  [SCREENS.GROUPS]: undefined;
  [SCREENS.MATCHING_USERS]: {
    event: Event | undefined;
  };
  [SCREENS.RATE_EVENT]: {
    event: Event;
  };
};

export type NavigationProps = NativeStackNavigationProp<RootStackParamsList>;
