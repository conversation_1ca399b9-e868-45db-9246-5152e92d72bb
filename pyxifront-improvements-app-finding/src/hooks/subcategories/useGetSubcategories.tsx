import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {SubCategoryType} from '~types/categories';

export function useGetSubcategories() {
  return useQuery<SubCategoryType[], Error>(
    'allSubCategories', // Query key
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}subcategories`, {
        method: 'GET',
        headers: {
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user children');
      }

      const data = await response.json();

      return data as SubCategoryType[];
    },
  );
}
