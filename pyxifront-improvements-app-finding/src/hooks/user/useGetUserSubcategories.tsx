import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetUserSubcategoriesResponse} from '~types/api/user';

export function useGetUserSubcategories(user_id: string) {
  return useQuery<GetUserSubcategoriesResponse, Error>(['userSubcategories', user_id], async () => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}users/${user_id}/subcategories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user subcategories');
    }

    const data = await response.json();

    return data as GetUserSubcategoriesResponse;
  });
}
