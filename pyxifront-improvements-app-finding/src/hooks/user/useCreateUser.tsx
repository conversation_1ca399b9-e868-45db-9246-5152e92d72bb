import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import FirebaseChatsService from '~services/FirebaseChats';
import {User} from '~types/api/user';

export function useCreateUser() {
  const queryClient = useQueryClient();

  const mutation = useMutation(
    async (userData: User) => {
      const token = await FirebaseAuth.getAuthToken();
      console.log(userData);
      const response = await fetch(`${Config.BASE_API_URL}users/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        throw new Error('Failed to create user');
      }

      const data = (await response.json()) as User;

      await FirebaseChatsService.createPyxiConciergeChat({
        user_id: data.uid,
        user_image: data.photo,
        user_name: `${data.first_name} ${data.last_name || ''}`,
      });
      return data;
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(['userAccount']);
      },
    },
  );

  return mutation;
}
