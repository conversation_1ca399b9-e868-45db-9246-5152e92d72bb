import {useMutation, useQueryClient} from 'react-query';
import {User} from '~types/api/user';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import auth from '@react-native-firebase/auth';

export function useUpdateUserPostCode() {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (postCode: string) => {
      const userId = auth().currentUser!.uid;

      const token = await FirebaseAuth.getAuthToken();
      

      const response = await fetch(`${Config.BASE_API_URL}users/${userId}/postal-code?postal_code=${postCode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      
      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      return response.json() as Promise<User>;
    },
    {onSuccess: () => queryClient.refetchQueries(['userAccount'])},
  );

  return mutation;
}
