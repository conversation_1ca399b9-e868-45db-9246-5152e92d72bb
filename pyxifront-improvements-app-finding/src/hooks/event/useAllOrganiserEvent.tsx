import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import {GetEventsParams} from '~types/events';

const NUMBER_OF_EVENTS_PER_PAGE = 10000;

export const useAllOrganiserEvent = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = false,
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  const fetchEvents = async ({pageParam = 0}) => {
    const token = await FirebaseAuth.getAuthToken();
    console.log(token, 'token');
    

    const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
    const limit = NUMBER_OF_EVENTS_PER_PAGE;

    const params: GetEventsParams = {
      limit,
      offset,
      tab,
      order_by,
      order_dir,
      distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe
    };
    
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    console.log(Config.BASE_API_URL + 'events/', {params, ...config}, 'Config.BASE_API_URL + {params, ...config}');
    
    const response = await axios.get(Config.BASE_API_URL + 'events/', {params, ...config});
    console.log(response, 'Config.BASE_API_URL + {params, ...config}');
    return response.data;
  };

  return useInfiniteQuery('eventsAllOrganise', fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const nextPage = lastPage.items.length === 10 ? pages.length : undefined;
      return nextPage;
    },
    enabled,
    cacheTime: 0,
  });
};
