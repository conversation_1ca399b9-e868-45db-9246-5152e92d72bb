import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import {GetEventsParams} from '~types/events';

const NUMBER_OF_EVENTS_PER_PAGE = 10000;

export const useAllEvents = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = false,
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe,
  isNeighbourhood = false
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  const fetchEvents = async ({pageParam = 0}) => {
    const token = await FirebaseAuth.getAuthToken();
    console.log(token, 'token');
    

    const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
    const limit = NUMBER_OF_EVENTS_PER_PAGE;

    const params: GetEventsParams = {
      limit,
      offset,
      tab: isNeighbourhood ? 'neighbourhood_event' : tab,
      order_by,
      order_dir,
      distance_km: isNeighbourhood ? 1000000000000000 : distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe
    };
    
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };

    const response = await axios.get(Config.BASE_API_URL + 'events/', {params, ...config});
    return response.data;
  };

  return useInfiniteQuery('eventsAll', fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const nextPage = lastPage.items.length === 10 ? pages.length : undefined;
      return nextPage;
    },
    enabled,
    cacheTime: 0,
  });
};
