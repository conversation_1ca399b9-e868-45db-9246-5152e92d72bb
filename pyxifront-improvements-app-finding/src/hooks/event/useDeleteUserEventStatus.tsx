import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {DeleteEventResponse} from '~types/api/event';

export function useDeleteUserEventStatus() {
  const queryClient = useQueryClient();

  return useMutation<DeleteEventResponse, Error, {event_id: number; user_id: string; email?: string}>(
    async ({user_id, event_id, email}: {event_id: number; user_id: string; email?: string}) => {
      const token = await FirebaseAuth.getAuthToken();

      const endPoint = user_id
        ? `${Config.BASE_API_URL}events/${event_id}/subscriptions/${user_id}`
        : `${Config.BASE_API_URL}events/${event_id}/external/subscriptions/${email}`;
      const response = await fetch(endPoint, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete the event');
      }

      const data = await response.json();

      return data;
    },
    {
      onSuccess: async () => {
        await queryClient.refetchQueries('eventPending');
        await queryClient.refetchQueries('eventAttendees');
      },
    },
  );
}
