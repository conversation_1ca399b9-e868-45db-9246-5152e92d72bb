import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {AddLikePayload} from '~types/api/event';

export function useLikeEvent() {
  const queryClient = useQueryClient();

  return useMutation<string, Error, AddLikePayload>(
    async ({eventId}: AddLikePayload) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${eventId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });
      console.log('add like', response);
      if (!response.ok) {
        throw new Error('Failed to like the event');
      }

      const data = await response.json();

      return data as string;
    },
    {onSuccess: () => queryClient.refetchQueries(['eventsAll'])},
  );
}
