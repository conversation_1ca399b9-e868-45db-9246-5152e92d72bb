import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import Config from 'react-native-config';
import {useMutation} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';

export const useCancelOrder = () => {
  const {t} = useTranslation();

  return useMutation(async (order_id: string) => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}orders/${order_id}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });
    console.log(response, token, 'responseresponse');
    

    if (response.status === 404) {
      Alert.alert(t('events.event_has_been_deleted'));
    }
    if (!response.ok) {
      throw new Error('Failed to cancel the order');
    }

    const data = await response.json();
    return data;
  });
};
