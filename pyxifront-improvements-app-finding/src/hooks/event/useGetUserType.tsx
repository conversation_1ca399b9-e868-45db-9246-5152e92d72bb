import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {UserType} from '~types/api/user';

export const useGetUserType = (user_id?: string) => {
  return useQuery<unknown, Error, UserType>(['userType'], async () => {
    if (!user_id) {
      return;
    }
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}accounts/${user_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to get user type');
    }

    const data = await response.json();

    return data;
  });
};
