import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {UpdateUserStatus} from '~types/api/event';

const useUpdateUserStatus = () => {
  const queryClient = useQueryClient();

  return useMutation<unknown, Error, UpdateUserStatus>(
    async (requestData: UpdateUserStatus) => {
      const token = await FirebaseAuth.getAuthToken();
      const endPoint = requestData.user_id
        ? `${Config.BASE_API_URL}events/${requestData.event_id}/subscriptions/${requestData.user_id}`
        : `${Config.BASE_API_URL}events/${requestData.event_id}/external/subscriptions/${requestData.email}`;
      const response = await fetch(
        endPoint,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: token,
          },
        },
      );

      if (!response.ok) {
        throw new Error('Failed to update user status');
      }

      const data = await response.json();

      return data;
    },
    {
      onSuccess: async () => {
        await queryClient.refetchQueries('eventAttendees');
        await queryClient.refetchQueries('eventPending');
      },
    },
  );
};

export default useUpdateUserStatus;
