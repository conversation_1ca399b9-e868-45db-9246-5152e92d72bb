import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {USER_EVENT_STATUS} from '~types/api/user';

export type StatusResponseType = {
  order_id: string;
  status: USER_EVENT_STATUS;
};

export function useGetEventUserStatus({event_id, user_id}: {event_id: number; user_id: string}) {
  return useQuery<StatusResponseType | undefined, Error>(['eventUserStatus', event_id], async () => {
    if (!event_id) {
      return undefined;
    }
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}events/${event_id}/subscriptions/${user_id}/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      return;
    }

    const data = await response.json();

    return data as StatusResponseType;
  });
}
