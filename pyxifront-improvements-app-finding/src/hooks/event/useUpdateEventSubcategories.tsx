import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {EditEventSubcategory} from '~types/api/event';

export const useUpdateEventSubcategories = () => {
  const queryClient = useQueryClient();

  return useMutation<unknown, Error, EditEventSubcategory>(
    async (requestData: EditEventSubcategory) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${requestData.event_id}/subcategories`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error('Failed update event subcategory');
      }

      const data = await response.json();

      return data;
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries('eventsAll');
      },
    },
  );
};
