import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Event} from '~types/api/event';
import {Alert, Linking} from 'react-native';

export function useUpdateEventMutation() {
  const queryClient = useQueryClient();

  return useMutation<Event, Error, Partial<Omit<Event, 'subcategories' | 'host_id' | 'user_liked' | 'likes_count'>>>(
    async (requestData: Partial<Omit<Event, 'subcategories' | 'host_id' | 'user_liked' | 'likes_count'>>) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${requestData.event_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error('Failed to update the event');
      }

      if (response.ok === true) {
        Alert.alert('Updated the event');
      }
      const data = await response.json();
      return data as Event;
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries('eventsAll');
      },
    },
  );
}
