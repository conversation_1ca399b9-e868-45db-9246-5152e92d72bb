import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {RemoveLikePayload} from '~types/api/event';

export function useRemoveLikeFromEvent() {
  const queryClient = useQueryClient();

  return useMutation<string, Error, RemoveLikePayload>(
    async ({eventId}: RemoveLikePayload) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${eventId}/like`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });
      console.log('remove like', response);

      if (!response.ok) {
        throw new Error('Failed to remove like from the event');
      }

      const data = await response.json();
      return data as string;
    },
    {onSuccess: () => queryClient.refetchQueries(['eventsAll'])},
  );
}
