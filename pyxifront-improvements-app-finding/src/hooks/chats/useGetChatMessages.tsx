import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import {ChatMessagesPayload, GetChatMessages} from '~types/chat';
import {useChatStore} from '~providers/chats/zustand';

export const NUMBER_OF_MESSAGES_PER_PAGE = 30;

const useGetChatMessages = (chat_id: number) => {
  const {chatItem, paginationUpdateChat} = useChatStore();

  const fetchChats = async () => {
    const token = await FirebaseAuth.getAuthToken();

    const limit = NUMBER_OF_MESSAGES_PER_PAGE;

    const params: ChatMessagesPayload = {
      limit,
      offset: chat_id === chatItem?.chatId ? chatItem.offset + NUMBER_OF_MESSAGES_PER_PAGE : 0,
      chat_id,
    };

    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };

    const response = await axios.get(Config.BASE_API_URL + `chats/${chat_id}/messages`, {params, ...config});

    return response.data;
  };

  return useInfiniteQuery('chat_messages', fetchChats, {
    getNextPageParam: (_: GetChatMessages, pages) => {
      // const nextPage = lastPage.items.length === NUMBER_OF_MESSAGES_PER_PAGE ? pages.length : undefined;
      return pages.length;
    },
    onSuccess: data => {
      const lastPage = data.pages[data.pages.length - 1] as GetChatMessages;

      if (!chat_id) {
        return;
      }

      paginationUpdateChat({messages: lastPage.items, chatId: chat_id, offset: lastPage.offset});
    },
    cacheTime: 0,
  });
};

export default useGetChatMessages;
