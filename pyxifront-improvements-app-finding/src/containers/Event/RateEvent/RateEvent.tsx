import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useRef, useState} from 'react';
import {View, StyleSheet, StatusBar, ImageBackground, Text, TouchableOpacity} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {GoBackHeader} from '~components/GoBackHeader';
import {NavigationProps} from '~types/navigation/navigation.type';
import StarRating from 'react-native-star-rating-widget';
import RatingSheet from '~components/RatingSheet';
import BottomSheet from '@gorhom/bottom-sheet';

const RateEvent: React.FC = () => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const navigation = useNavigation<NavigationProps>();
  const route = useRoute<any>();
  const insects = useSafeAreaInsets();
  const {event} = route.params;
  const [rating, setRating] = useState(0);
  const [isSubmitted, setSubmitted] = useState(false);

  const goBackHandler = () => {
    navigation.goBack();
  };

  const onSubmit1 = () => {
    setSubmitted(true);
    bottomSheetRef.current?.snapToIndex(0);
  };

  const onSubmit = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle={'dark-content'} />
      <GoBackHeader customCallback={goBackHandler} isGradientShow={false} />
      <ImageBackground
        style={[
          styles.container,
          {justifyContent: 'flex-end', alignItems: 'center', paddingBottom: insects.bottom + 20},
        ]}
        source={{uri: event.image_url}}>
        {isSubmitted && <Text style={[styles.eventNameText, {top: insects.top + 45}]}>{event.name}</Text>}
        <View style={styles.overlay} />
        <Text style={styles.eventTitle}>What did you think of {'\n' + event.name}? </Text>
        <StarRating rating={rating} onChange={setRating} color="#F5A865" emptyColor={'#AEAEAE'} starSize={30} />
        <TouchableOpacity onPress={onSubmit1} style={styles.button}>
          <Text style={styles.buttonText}>{'Submit'}</Text>
        </TouchableOpacity>
      </ImageBackground>
      <RatingSheet bottomSheetRef={bottomSheetRef} rate={rating} onSubmit={onSubmit} />
    </View>
  );
};

const styles = StyleSheet.create({
  eventNameText: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontSize: 20,
    marginBottom: 8,
    position: 'absolute',
    zIndex: 10000,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#4A48AD',
    paddingVertical: 10,
    width: '88%',
    borderRadius: 20,
    marginHorizontal: 10,
    marginTop: 30,
  },
  eventTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontSize: 20,
    marginBottom: 8,
  },
  container: {flex: 1, backgroundColor: '#fff'},
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.75)',
  },
});

export default RateEvent;
