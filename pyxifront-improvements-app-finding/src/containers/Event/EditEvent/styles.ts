import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  buttonContainer1: {
    flex: 1,
    backgroundColor: '#8E8E93',
  },
  buttonContainer2: {
    flex: 1,
    backgroundColor: 'red',
  },
  rowView: {
    flexDirection: 'row',
    marginTop: 5,
  },
  safeAreaView: {
    flex: 1,
    paddingBottom: 20,
    marginTop: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContentContainerStyle: {
    flexGrow: 0,
    padding: 16,
  },
  updateEventText: {
    lineHeight: 41,
    fontSize: 34,
    color: '#1D1E20',
    fontWeight: '700',
  },
  buttonContainer: {
    paddingHorizontal: 16,
  },
  buttonContainerStyle: {
    backgroundColor: '#FF9500',
  },
  buttonTextStyle: {
    color: '#fff',
  },
});

export default styles;
