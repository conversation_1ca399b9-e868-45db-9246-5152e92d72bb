import { useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, Text, TextInput, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { organizeByCategory } from '~Utils/filterSubcategories';
import Button from '~components/Button';
import { GoBackHeader } from '~components/GoBackHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import { useGetCategories } from '~hooks/subcategories/useGetCategories';
import { useGetSubcategories } from '~hooks/subcategories/useGetSubcategories';
import { useUpdateEventState } from '~providers/updateEvent/zustand';
import { SubCategoryType } from '~types/categories';
import { NavigationProps } from '~types/navigation/navigation.type';
import styles from './styles';
import { logScreenView } from '~Utils/firebaseAnalytics';

const isAndroid = Platform.OS === 'android';

const EditSubcategory = () => {
  const {top, bottom} = useSafeAreaInsets();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {subcategory, setSubCategory} = useUpdateEventState();
  const {goBack} = useNavigation<NavigationProps>();
  const {t} = useTranslation();

  const [subCatsList, setSubCatsList] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    if (d && data && !searchValue) {
      setSubCatsList(organizeByCategory([], d, data));
    }
  }, [d, data, searchValue]);

  useEffect(() => {
    handleSearchCategory()
  }, [searchValue])

  useEffect(() => {
    logScreenView('Edit Subcategory', 'EditSubcategory');
  }, []);

  const handleSearchCategory = () => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      setSubCatsList(organizeByCategory([], d, inputData));
    } else if (data && d) {
      setSubCatsList(organizeByCategory([], d, data));
    }
  };

  return (
    <>
      <GoBackHeader />
      <View style={[styles.container, {paddingTop: top + 40}]}>


        <View style={{width: '100%', alignItems: 'flex-start', paddingHorizontal: 16}}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: 24,
              color: '#1D1E20',
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('signin.choosing_category')}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 16,
          }}>
          <TextInput
            placeholder="Search by name"
            value={searchValue}
            onChangeText={setSearchValue}
            style={{
              flex: 1,
              height: 40,
              borderWidth: 1,
              borderColor: '#888',
              borderRadius: 5,
              paddingHorizontal: 10,
              marginRight: 8,
            }}
          />
          <Button
            label="Search"
            onPress={handleSearchCategory}
            containerStyle={{backgroundColor: '#FF9500', height: 40, borderRadius: 5}}
            textStyle={{color: 'white', fontWeight: '600'}}
          />
        </View>

        <View style={styles.subcategoriesListsContainer}>
          <SubcategoriesLists
            categoriesList={d}
            searchValue={searchValue} 
            filteredSubcategoriesData={subCatsList}
            selectedCategories={subcategory?.[0]?.subcategory_id ? [subcategory[0].subcategory_id] : []}
            onCategoryChange={value => {
              const currentSubcat = data?.find(sub => sub.subcategory_id === value)!;
              setSubCategory(currentSubcat);
            }}
          />
        </View>

        <View
          style={[
            styles.buttonContainer,
            {
              paddingBottom: isAndroid ? bottom + 20 : bottom,
            },
          ]}>
          <Button
            label="Done"
            onPress={goBack}
            textStyle={styles.buttonTextStyles}
            containerStyle={styles.buttonContainerStyles}
          />
        </View>
      </View>
    </>
  );
};

export default EditSubcategory;
