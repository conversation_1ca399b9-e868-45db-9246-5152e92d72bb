import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import axios from 'axios';
import moment from 'moment-timezone';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Image, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Config from 'react-native-config';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {CheckIcon} from '~assets/icons';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import {useCreateEvent} from '~hooks/event/useCreateEvent';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUpdateEventMutation} from '~hooks/event/useUpdateEvent';
import {useUpdateEventSubcategories} from '~hooks/event/useUpdateEventSubcategories';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';

const isAndroid = Platform.OS === 'android';

const PublishEventModal = () => {
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const [buttonIsDisabled, setButtonIsDisabled] = useState(false);
  const {eventInfo, groups, image, subcategory, reset} = useEventCreationStore();
  const {mutateAsync: createEventAsync} = useCreateEvent();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {mutateAsync: updateEventMutation} = useUpdateEventMutation();
  const {mutateAsync: updateSubcategory} = useUpdateEventSubcategories();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.CREATE_EVENT_SUBMITTING>>();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Publish Event', 'PublishEventModal');
  }, []);

  const getAndSetPastCategory = async (subcategory: number[]) => {
    try {
      const pastSub = await AsyncStorage.getItem('subcategory');
      const subCatList = JSON.parse(pastSub || "[]");
  
      const updatedCat = [...subcategory, ...subCatList];
      const firstThreeItems = updatedCat.slice(0, 3);
  
      await AsyncStorage.setItem('subcategory', JSON.stringify(firstThreeItems));
  
      console.log(firstThreeItems, 'Saved subcategories');
    } catch (error) {
      console.error('Error saving subcategories:', error);
    }
  };

  const onSubmit = async () => {
    getAndSetPastCategory(subcategory);
    setIsLoading(true);
    setButtonIsDisabled(true);
    try {
      const apiKey = Config.GOOGLE_API_KEY; // Get the API key from your config
      const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${eventInfo.coords.lat},${eventInfo.coords.long}&key=${apiKey}&language=en`;

      // Perform the GET request using axios
      const response = await axios.get(url);

      let coord_address;
      // Check if we got valid results
      if (response.data.results && response.data.results.length > 0) {
        coord_address = response.data.results[0].formatted_address;
      } else {
        coord_address = null;
      }
      const tickets: any = eventInfo.ticketTypes.map(item => {
        const ticketItem = {
          name: item.name,
          currency: item.currency,
          price: item.price,
          discounted_price: item.discountedPrice,
          capacity: item.numberOfTickets,
          description: item.description,
        };
        return ticketItem;
      });
      const promo_codes: any = eventInfo.promoCodes.map(item => {
        const promoItem = {
          name: item.name,
          description: item.description,
          discount_percentage: item.percentDiscount,
          minimum_tickets: 0,
          minimum_amount: 0,
        };
        return promoItem;
      });
      if (route.params?.item) {
        //@ts-ignore
        await updateEventMutation({
          event_id: route.params?.item?.event_id || 0,
          name: eventInfo.name,
          description: eventInfo.description,
          end_date: eventInfo.end_date,
          start_date: eventInfo.start_date,
          private: !!eventInfo.private,
          image_url: image,
          number_slots: eventInfo.number_slots,
          is_exported: false,
          coords: eventInfo.coords,
          event_type: userType || 'personal',
          address_name: eventInfo.address_name,
          is_for_kids: eventInfo.isForKids,
          payment_url: eventInfo.payment_url,
          is_paid: eventInfo.is_paid,
          coord_address: coord_address,
          tickets: tickets,
          promo_codes: promo_codes,
        });
        if (subcategory && subcategory.length > 0) {
          await updateSubcategory({
            event_id: route.params?.item?.event_id || 0,
            ids: [subcategory[0]],
          });
        }
        reset();
        setIsLoading(false);
        navigation.pop(4);
      } else {
        //@ts-ignore
        await createEventAsync({
          name: eventInfo.name,
          description: eventInfo.description,
          end_date: eventInfo.end_date,
          start_date: eventInfo.start_date,
          private: !!eventInfo.private,
          image_url: image,
          subcategories: subcategory,
          number_slots: eventInfo.number_slots,
          is_exported: false,
          coords: eventInfo.coords,
          event_type: userType || 'personal',
          address_name: eventInfo.address_name,
          is_for_kids: eventInfo.isForKids,
          payment_url: eventInfo.payment_url,
          is_paid: eventInfo.is_paid,
          coord_address: coord_address,
          tickets: tickets,
          promo_codes: promo_codes,
        });
        reset();
        setIsLoading(false);
        navigation.reset({index: 0, routes: [{name: SCREENS.HOME}]});
      }
    } catch (e) {
      setIsLoading(false);
      console.log(e, 'submit event error');
      Alert.alert('Something went wrong');
      console.log(e);
      setButtonIsDisabled(false);
    }
  };

  useEffect(() => {
    setButtonIsDisabled(false);
  }, []);

  return (
    <View style={styles.container}>
      <GoBackHeader isGradientShow={false} />
      <ScrollView style={styles.topContainer}>
        <Image style={styles.eventIcon} source={require('~assets/images/EventIcon.png')} />
        <Text style={styles.publishText}>{t('events.publish_event')}</Text>
        <View style={styles.itemContainer}>
          <CheckIcon color="#30DB5B" />
          <View style={styles.rightSection}>
            <Text style={styles.label}>{eventInfo.name}</Text>
            <Text style={styles.additionalText}>{eventInfo.description}</Text>
          </View>
          <TouchableOpacity onPress={() => navigation.pop(2)}>
            <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
          </TouchableOpacity>
        </View>
        <View style={styles.itemContainer}>
          <CheckIcon color="#30DB5B" />
          <View style={styles.rightSection}>
            <Text style={styles.label}>{t('events.date_and_time')}</Text>
            <Text style={styles.additionalText}>{`${t('generic.start')} ${moment(eventInfo.start_date).format(
              'DD/MM/YYYY h:mm A',
            )}`}</Text>
            <Text style={styles.additionalText}>{`${t('generic.end')} ${moment(eventInfo.end_date).format(
              'DD/MM/YYYY h:mm A',
            )}`}</Text>
          </View>
          <TouchableOpacity onPress={() => navigation.pop(2)}>
            <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
          </TouchableOpacity>
        </View>
        <View style={styles.itemContainer}>
          <CheckIcon color="#30DB5B" />
          <View style={styles.rightSection}>
            <Text style={styles.label}>{t('generic.location')}</Text>
            <Text style={styles.additionalText}>{eventInfo.address_name || eventInfo?.locationIsChosen?.address || ""}</Text>
          </View>
          <TouchableOpacity onPress={() => navigation.pop(2)}>
            <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
          </TouchableOpacity>
        </View>
        <View style={styles.itemContainer}>
          <CheckIcon color="#30DB5B" />
          <View style={styles.rightSection}>
            <Text style={styles.label}>{'Visibility'}</Text>
            <Text style={styles.additionalText}>{t('events.maximum_people', {capacity: eventInfo.number_slots})}</Text>
          </View>
          <TouchableOpacity onPress={() => navigation.pop(2)}>
            <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
          </TouchableOpacity>
        </View>
        <View style={styles.itemContainer}>
          <CheckIcon color="#30DB5B" />
          <View style={styles.rightSection}>
            <Text style={styles.label}>{t('events.who_is_this_for')}</Text>
            <Text style={styles.additionalText}>
              {groups.map(group => t(`onboarding.groups_${group}`)).join(' & ')}
            </Text>
          </View>
          <TouchableOpacity onPress={() => navigation.pop()}>
            <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
          </TouchableOpacity>
        </View>
        {eventInfo.ticketTypes.length > 0 && (
          <View style={[styles.itemContainer, {alignItems: 'flex-start'}]}>
            <CheckIcon color="#30DB5B" />
            <View style={styles.rightSection}>
              <Text style={styles.label}>{t('events.ticket_info')}</Text>
              {eventInfo.ticketTypes.map((ticket, index) => (
                <View key={index} style={styles.ticketItem}>
                  <Text style={styles.ticketName}>{ticket.name}</Text>
                  <Text style={styles.ticketDetails}>
                    {t('events.price')}: ${ticket.price} | {t('events.quantity')}: {ticket.numberOfTickets}
                  </Text>
                </View>
              ))}
            </View>
            <TouchableOpacity onPress={() => navigation.pop(2)}>
              <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
            </TouchableOpacity>
          </View>
        )}
        {eventInfo.promoCodes.length > 0 && (
          <View style={[styles.itemContainer, {alignItems: 'flex-start'}]}>
            <CheckIcon color="#30DB5B" />
            <View style={styles.rightSection}>
              <Text style={styles.label}>{t('events.promo_info')}</Text>
              {eventInfo.promoCodes.map((ticket, index) => (
                <View key={index} style={styles.ticketItem}>
                  <Text style={styles.ticketName}>{ticket.name}</Text>
                  <Text style={styles.ticketDetails}>
                    {t('settings.name')}: {ticket.name} | {t('events.discount')}: {ticket.percentDiscount}%
                  </Text>
                </View>
              ))}
            </View>
            <TouchableOpacity onPress={() => navigation.pop(2)}>
              <Image style={styles.editIcon} source={require('../../../../assets/icons/edit.png')} />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
      <View style={{marginBottom: isAndroid ? bottom + 20 : bottom, paddingHorizontal: 30}}>
        <Button
          disabled={buttonIsDisabled}
          label={route.params?.item ? t('events.update_event') : t('generic.publish')}
          isLoading={isLoading}
          onPress={onSubmit}
          containerStyle={{backgroundColor: '#FF9500'}}
          textStyle={{color: '#fff'}}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ticketItem: {
    marginTop: 4,
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  ticketName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  ticketDetails: {
    fontSize: 14,
    color: '#666',
  },
  editIcon: {
    width: 20,
    height: 20,
  },
  container: {
    flex: 1,
    backgroundColor: '#FCF9ED',
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    paddingTop: 44,
  },
  topContainer: {
    flex: 1,
  },
  eventIcon: {
    width: 64,
    height: 64,
    alignSelf: 'center',
  },
  publishText: {
    lineHeight: 41,
    fontSize: 34,
    fontWeight: '700',
    color: '#000000',
    marginVertical: 42,
    textAlign: 'center',
  },
  itemContainer: {
    flexDirection: 'row',
    paddingHorizontal: 32,
    paddingBottom: 32,
    alignItems: 'center',
  },
  rightSection: {
    marginLeft: 14,
    marginEnd: 10,
    flex: 1,
  },
  label: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '600',
  },
  additionalText: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '400',
    color: 'rgba(60, 60, 67, 0.6)',
  },
});

export default PublishEventModal;
