import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  flatList: {
    paddingHorizontal: 16,
    marginTop: 16,
  },
  fleaListContentContainerStyle: {
    gap: 12,
  },
  attendeesText: {
    color: '#000',
    fontWeight: '700',
    fontSize: 19,
    lineHeight: 22,
    paddingHorizontal: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flex: 1,
  },
  image: {
    height: 40,
    width: 40,
    borderRadius: 12,
    overflow: 'hidden',
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  buttonContainer: {
    backgroundColor: '#2E2EAB',
    borderRadius: 50,
  },
  buttonRejectContainer: {
    backgroundColor: 'red',
    borderRadius: 50,
  },
  buttonText: {
    fontSize: 15,
    lineHeight: 16,
    fontWeight: '500',
    color: '#fff',
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 6,
    paddingLeft: 10,
  },
});

export default styles;
