import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, ListRenderItem, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {FadeInLeft} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import {useGetEventPending} from '~hooks/event/useGetEventPending';
import useUpdateUserStatus from '~hooks/event/useUpdateUserStatus';
import FirebaseChatsService from '~services/FirebaseChats';
import {User} from '~types/api/user';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import styles from './styles';
import {logScreenView} from '~Utils/firebaseAnalytics';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

const PendingAttendeesScreen = () => {
  const {t} = useTranslation();
  const {navigate} = useNavigation<NavigationProps>();
  const {top, bottom} = useSafeAreaInsets();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.PENDING_ATTENDEES>>();
  const {eventId} = route.params;

  const {mutateAsync: updateUserStatus} = useUpdateUserStatus();
  const {mutateAsync: deleteEventUserStatus} = useDeleteUserEventStatus();
  const {data} = useGetEventPending({event_id: eventId});

  const [isLoading, setIsLoading] = useState(false);
  const [isReject, setIsReject] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
    setIsReject(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Pending Attendees', 'PendingAttendeesScreen');
  }, []);

  const renderItem: ListRenderItem<{user: User; event_id: number; status: string; name: string; email: string}> = ({
    item,
    index,
  }) => (
    <AnimatedTouchableOpacity
      key={item.user?.uid}
      entering={FadeInLeft.delay(100 * index + 50).duration(300)}
      style={styles.itemContainer}
      disabled={!item.user}
      onPress={() => {
        navigate(SCREENS.PERSONAL_INFO, {
          user: {
            tag: index + 'pending_image',
            source: item.user.photo,
            name: item.user.last_name ? `${item.user.first_name} ${item.user.last_name}` : item.user.first_name,
            description: item?.user.description,
            user_id: item.user.uid,
          },
        });
      }}>
      <View style={styles.leftContainer}>
        {item.user && (
          <View style={styles.image}>
            <FastImage
              style={StyleSheet.absoluteFillObject}
              resizeMode={'cover'}
              source={{uri: item.user.photo, priority: 'normal'}}
            />
            <Animated.Image
              sharedTransitionTag={index + 'pending_image'}
              source={{uri: item.user.photo}}
              style={styles.image}
            />
          </View>
        )}

        <Text style={styles.name}>{item.user ? item.user.first_name : item.name}</Text>
      </View>
      <View style={styles.buttonsContainer}>
        <Button
          label={t('generic.accept')}
          isLoading={isLoading}
          onPress={async () => {
            setIsLoading(true);
            await updateUserStatus({event_id: eventId, user_id: item.user?.uid, email: item.email});
            if (item.user) {
              await FirebaseChatsService.addNewUserToTheGroupChat({
                user_id: item.user.uid,
                user_name: item.user.first_name,
                user_image: item.user!.photo,
                event_id: eventId,
              });
            }
          }}
          containerStyle={styles.buttonContainer}
          textStyle={styles.buttonText}
        />
        <Button
          label={t('generic.reject')}
          isLoading={isReject}
          onPress={async () => {
            setIsReject(false);
            await deleteEventUserStatus({event_id: eventId, user_id: item.user?.uid, email: item.email});
          }}
          containerStyle={styles.buttonRejectContainer}
          textStyle={styles.buttonText}
        />
      </View>
    </AnimatedTouchableOpacity>
  );

  return (
    <>
      <GoBackHeader />

      <Text
        style={[
          styles.attendeesText,
          {
            marginTop: top + 100,
          },
        ]}>
        {data ? data.length + ' ' + t('events.attendees_pending') : 'There is no pending attendees yet'}
      </Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        style={[styles.flatList, {marginBottom: bottom + 10}]}
        contentContainerStyle={styles.fleaListContentContainerStyle}
      />
    </>
  );
};

export default PendingAttendeesScreen;
