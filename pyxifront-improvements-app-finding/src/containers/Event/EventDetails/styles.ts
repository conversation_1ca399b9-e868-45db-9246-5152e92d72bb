import {Link} from '@react-navigation/native';
import {Dimensions, StyleSheet} from 'react-native';
const BACKGROUND = '#FCF9ED';
const BACKGROUND_NEUTRAL = '#f4f4f4';
const ACCENT = '#ba8638';
const TEXT = '#222325';
const LINK = '#2980b9';

const styles = StyleSheet.create({
  attendeesCount: {
    color: '#4A48AD',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 22,
  },
  profileImg: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FEFBEE',
  },
  attendeeRowStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spinnerView: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    fontWeight: 'bold',
  },
  buttonView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentTextView: {
    fontSize: 12,
    marginLeft: 10,
  },
  userImage: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'gray',
  },
  commentItemView: {
    flexDirection: 'row',
    marginTop: 10,
  },
  commentTitle: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  commentView: {
    backgroundColor: '#f2f2f2',
    borderRadius: 15,
    padding: 15,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#AEAEAE',
    padding: 15,
  },
  flex: {
    flex: 1,
  },
  attendeeLeftContainer: {
    flexDirection: 'row',
    flex: 1,
    paddingRight: 10,
  },
  attendeePhoto: {
    height: 40,
    width: 40,
    borderRadius: 12,
    overflow: 'hidden',
  },
  attendeeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  attendeeSub: {
    flex: 1,
    marginLeft: 15,
    fontSize: 10,
    color: 'gray',
  },
  attendeeName: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
    fontSize: 17,
    lineHeight: 20,
    fontWeight: '500',
    color: 'black',
    alignSelf: 'center',
  },
  attendeeRole: {
    fontSize: 12,
    lineHeight: 20,
    fontWeight: '400',
    color: '#74747B',
  },
  checkContainer: {
    flexDirection: 'row',
  },
  checkRightContainer: {
    marginLeft: 14,
    flex: 1
  },
  loaderJoinBtnView: {
    aspectRatio: 1,
    width: 100,
    height: 80,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkLabel: {
    fontWeight: '600',
    fontSize: 15,
    lineHeight: 20,
    color: '#000000',
  },
  checkDescription: {
    color: 'rgba(60, 60, 67, 0.6)',
    fontWeight: '400',
    fontSize: 15,
    lineHeight: 20,
    flex: 1
  },
  divider: {
    backgroundColor: '#E5E5EA',
    height: 1,
    marginVertical: 16,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    right: 0,
    alignItems: 'center',
  },
  artistContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  artist: {
    textAlign: 'center',
    color: 'white',
    fontSize: 48,
    fontWeight: 'bold',
  },
  tracks: {
    paddingTop: 32,
    backgroundColor: 'white',
  },
  buttonRejectContainer: {
    backgroundColor: 'red',
    borderRadius: 50,
  },
  buttonText: {
    fontSize: 15,
    lineHeight: 16,
    fontWeight: '500',
    color: '#fff',
  },
  likeContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  textRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  verifiedIcon: {
    width: 35,
    height: 35,
  },
  customMarkerContainer: {
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  markerContent: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 8,
    marginHorizontal: 20,
    height: 60,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5, // For Android shadow
    alignItems: 'center',
    position: 'relative', // For absolute positioning of the arrow
  },
  addressText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  arrowContainer: {
    position: 'absolute',
    bottom: 0, // Position the arrow outside the bubble
    left: '50%',
    transform: [{translateX: -15}], // Center the arrow
    zIndex: 1, // Ensure the arrow appears above the background
  },
  arrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 10,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#fff', // Same color as the background
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
  },
});

const detailStyles = StyleSheet.create({
  background: {
    backgroundColor: BACKGROUND,
    flex: 1,
  },
  imageWrapper: {
    height: 300,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
    width: undefined,
    height: undefined,
  },
  header: {
    fontSize: 40,
    color: TEXT,
  },
  price: {
    fontSize: 46,
    color: ACCENT,
  },
  priceWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  description: {
    color: TEXT,
    fontSize: 16,
    lineHeight: 25,
  },
  readMoreTest: {
    color: '#0645AD',
  },
  callToAction: {
    backgroundColor: 'black',
    padding: 16,
    borderRadius: 50,
    width: '100%',
    marginBottom: 48,
  },
  callToActionText: {
    color: 'white',
    textAlign: 'center',
    textTransform: 'uppercase',
    fontSize: 22,
    fontWeight: 'bold',
  },
  margin: {
    marginBottom: 24,
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
    paddingTop: 32,
  },
  picker: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    backgroundColor: BACKGROUND_NEUTRAL,
    borderRadius: 20,
  },
  pickerItem: {
    padding: 8,
    width: 90,
    borderRadius: 20,
    textAlign: 'center',
  },
  pickerItemSelected: {
    borderWidth: 1,
    borderColor: TEXT,
  },
  linkColor: {
    color: LINK,
  },
});

export {styles, detailStyles};
