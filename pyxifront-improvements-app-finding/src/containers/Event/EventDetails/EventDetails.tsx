import auth from '@react-native-firebase/auth';
import {RouteProp, useFocusEffect, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {t} from 'i18next';
import moment from 'moment-timezone';
import React, {useEffect, useRef, useState, useCallback} from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Linking,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TextLayoutEventData,
  TouchableOpacity,
  View,
} from 'react-native';
import RNCalendarEvents from 'react-native-calendar-events';
import FastImage from 'react-native-fast-image';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';
import Animated, {FadeInDown, StretchInY, interpolate} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {LONG_DATE_FORMAT} from '~Utils/Time';
import {openLocationInMaps} from '~Utils/openLocationInMaps';
import {BigCheckIcon, BigPendingIcon, CheckIcon} from '~assets/icons';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import LikeButton from '~components/LikeButton';
import ModalWithJoin from '~components/ModalWithItems/ModalWithJoin';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import {useGetEventAttendees} from '~hooks/event/useGetEventAttendees';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {useGetEventUserStatus} from '~hooks/event/useGetEventUserStatus';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import {Business} from '~types/api/business';
import {SUBSCRIPTION_STATUS, USER_EVENT_STATUS, User} from '~types/api/user';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {detailStyles, styles} from './styles';
import * as AddCalendarEvent from 'react-native-add-calendar-event';
import firestore from '@react-native-firebase/firestore';
import QRCodeGenerator from '~components/QR-codes/QRCodeGenerator';
import ScannerQRCode from '~components/QR-codes/ScannerQRCode';
import ModalWithCalendar from '~components/ModalWithItems/ModalWithCalendar/ModalWithCalendar';
import EventDetailsPlaceholder from './EventDetailsPlaceholder';
import Hyperlink from 'react-native-hyperlink';
import Calendar from '~components/Calendar';
import {Dialog} from 'react-native-simple-dialogs';
import axios from 'axios';
import {BottomSheetModalProvider, BottomSheetModal} from '@gorhom/bottom-sheet';
import {useUpdateUserPostCode} from '~hooks/user/useUpdateUserPostcode';
import CommentSheet from '~components/CommentSheet';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetCommentByEvent} from '~hooks/event/useGetCommentByEvent';
import {useCreateComment} from '~hooks/event/useCreateComment';
import AnimatedLoadingModal from '~components/Matching/AnimatedLoadingModal';
import {useCreateShortUrl} from '~hooks/event/useCreateShortUrl';
import Share from 'react-native-share';
import {getMessageOfRecurrence} from '~Utils/event';
import {ChatType} from '~types/chat';
import {useGetEventMatchingStatus} from '~hooks/event/useGetEventMatchingStatus';
import Config from 'react-native-config';
import IssueModal from '~types/components/issueModal';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {BlurView} from '@react-native-community/blur';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';

const {height} = Dimensions.get('window');
const isAndroid = Platform.OS === 'android';
const TRUNCATE_DESCRIPTION_NUMBER_OF_LINES = 6;

const DATE_FORMAT = 'ddd ' + LONG_DATE_FORMAT;

const MatchingOptionsSheet = ({
  bottomSheetRef,
  onOptionSelect,
}: {
  bottomSheetRef: React.RefObject<BottomSheetModal>;
  onOptionSelect: (type: 'neighbours' | 'anyone' | null) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleChange = useCallback((index: number) => {
    setIsOpen(index >= 0);
  }, []);

  return (
    <>
      <BottomSheetModal
        ref={bottomSheetRef}
        snapPoints={['40%']}
        index={0}
        enablePanDownToClose
        backdropComponent={({animatedIndex}) => (
          <Animated.View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              opacity: interpolate(animatedIndex.value, [0, 1], [1, 0]),
            }}>
            <BlurView style={StyleSheet.absoluteFill} blurType="light" blurAmount={5} />
          </Animated.View>
        )}
        backgroundStyle={{backgroundColor: '#fff', borderRadius: 24}}
        handleIndicatorStyle={{backgroundColor: '#757575', width: 50}}
        onChange={handleChange}>
        <View style={{padding: 20}}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24}}>
            <Text style={{fontSize: 20, fontWeight: '600', color: '#000'}}>Choose Matching Option</Text>
            <TouchableOpacity
              onPress={() => bottomSheetRef.current?.close()}
              style={{
                padding: 8,
                backgroundColor: '#f5f5f5',
                borderRadius: 20,
              }}>
              <Text style={{fontSize: 16, color: '#666'}}>✕</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={{
              padding: 16,
              borderRadius: 12,
              backgroundColor: '#f8f8f8',
              marginBottom: 12,
              borderWidth: 1,
              borderColor: '#eee',
              elevation: 1,
            }}
            onPress={() => {
              onOptionSelect('neighbours');
              bottomSheetRef.current?.close();
            }}>
            <Text style={{fontSize: 16, color: '#2E2EAB', fontWeight: '500'}}>Match with neighbours only</Text>
            <Text style={{fontSize: 14, color: '#666', marginTop: 4}}>
              Find people who live nearby to join events together
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              padding: 16,
              borderRadius: 12,
              backgroundColor: '#f8f8f8',
              borderWidth: 1,
              borderColor: '#eee',
              elevation: 1,
            }}
            onPress={() => {
              onOptionSelect('anyone');
              bottomSheetRef.current?.close();
            }}>
            <Text style={{fontSize: 16, color: '#2E2EAB', fontWeight: '500'}}>Match with anyone in Pyxi</Text>
            <Text style={{fontSize: 14, color: '#666', marginTop: 4}}>
              Open to meeting anyone who shares your interests
            </Text>
          </TouchableOpacity>
        </View>
      </BottomSheetModal>
    </>
  );
};

const addEventToCalendar = async (title: any, startDate: any, endDate: any, description: any, location: any) => {
  const now = new Date();
  const startDateEvent = new Date(startDate);
  const effectiveStartDate = startDateEvent < now ? now : startDateEvent;

  try {
    const authStatus = await RNCalendarEvents.requestPermissions();
    if (authStatus === 'authorized') {
      const eventId = await RNCalendarEvents.saveEvent(title, {
        startDate: effectiveStartDate.toISOString(),
        endDate: new Date(endDate).toISOString(),
        description: description,
        location: location,
      });

      if (Platform.OS === 'ios') {
        AddCalendarEvent.presentEventViewingDialog({eventId: eventId})
          .then(event => {
            Alert.alert('Calendar', 'Your event has been saved.');
          })
          .catch(error => {
            console.error('Error presenting event viewing dialog', error);
            Alert.alert('Error', 'Failed to open the event viewer: ' + error.message);
          });
      } else {
        Alert.alert('Unsupported Feature', 'This feature is only available on iOS.');
      }
    } else {
      console.error('Calendar permission is not authorized');
      return;
    }
  } catch (error) {
    console.error('Failed to add event to calendar:', error);
  }
};

const addCalendarEvent = async (eventConfig: any) => {
  try {
    const eventInfo = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
    setTimeout(() => {
      if (eventInfo?.action !== 'CANCELED') {
        Alert.alert('Calendar', 'Your event has been saved.');
      } else {
        Alert.alert('Calendar', 'Your event has been cancelled.');
      }
    }, 2000);
  } catch (error) {
    Alert.alert('Error', 'Failed to add event to calendar');
    console.error('Add event error:', error);
  }
};

const getRandomItem = (arr: Array<any>) => {
  return arr[Math.floor(Math.random() * arr.length)];
};

const randomUserArray1 = [
  require('../../../assets/images/user1.png'),
  require('../../../assets/images/user2.png'),
  require('../../../assets/images/user3.png'),
  require('../../../assets/images/user4.png'),
  require('../../../assets/images/user5.png'),
  require('../../../assets/images/user6.png'),
  require('../../../assets/images/user7.png'),
  require('../../../assets/images/user8.png'),
];

const randomUserArray2 = [
  require('../../../assets/images/user9.png'),
  require('../../../assets/images/user10.png'),
  require('../../../assets/images/user11.png'),
  require('../../../assets/images/user12.png'),
  require('../../../assets/images/user13.png'),
  require('../../../assets/images/user14.png'),
  require('../../../assets/images/user15.png'),
];

type Group = any; // Add proper type if available

const isWithin12HoursOfStart = (startDate: string | undefined, isPyxiSelect: boolean): boolean => {
  if (!startDate) {
    return false;
  }

  // Explicitly use local time for 'now'
  const now = moment().local();
  const eventStart = moment(startDate);

  // Check if the parsed date is valid
  if (!eventStart.isValid()) {
    console.warn(`Invalid date provided: ${startDate}`);
    return false;
  }

  // Calculate difference in milliseconds for precision
  const diffInMs = eventStart.diff(now);
  const diffInHours = diffInMs / (1000 * 60 * 60); // Convert to hours

  // Log for debugging in local time
  console.debug({
    now: now.format('YYYY-MM-DD HH:mm:ss Z'), // Local time with time zone offset
    startDate,
    diffInHours,
  });

  return diffInHours >= 0 && diffInHours <= 12 && isPyxiSelect;
};
export function EventDetails() {
  const {top} = useSafeAreaInsets();
  const calendarRef = React.useRef<{open: () => void; close: () => void}>();
  const matchingOptionsSheetRef = useRef<BottomSheetModal>(null);
  const commentSheetRef = useRef<BottomSheetModal>(null);
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.HOME_EVENT>>();
  const navigation = useNavigation<NavigationProps>();
  const {tag, eventId, item: itemFromRoute} = route.params;
  const [descriptionNumberOfLines, setDescriptionNumberOfLines] = React.useState(TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  const [lengthMore, setLengthMore] = React.useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const {data: pyxiHost} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');

  const {data: fetchedItem, refetch: refetchEvent, isLoading: isEventLoading} = useGetEventById(eventId);
  const {data: matchingStatus, refetch: refetchMatchinEventSatus} = useGetEventMatchingStatus(eventId + '');
  const {
    data: commentList,
    refetch: refetchCommentEvent,
    isLoading: isCommentEventLoading,
  } = useGetCommentByEvent(eventId);
  const {mutateAsync: createComment} = useCreateComment();
  const {data: eventAttendees, refetch: refetchAttendees} = useGetEventAttendees({
    event_id: eventId,
    subscription_status: SUBSCRIPTION_STATUS.ACCEPTED,
  });

  const {
    data: userStatus,
    isLoading: userStatusIsLoading,
    isFetching: userStatusIsFetching,
    refetch,
  } = useGetEventUserStatus({event_id: eventId, user_id: auth()!.currentUser?.uid || ''});

  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus();
  const {openMatchingLoadingModal, setCurrentMatchingEvent, setDomain, setRefresh, setMatchingType, matchingType} =
    useMatchingLoadingModalAnimation();
  const {data: userAccount, refetch: refetchUser} = useGetUserAccount(auth().currentUser?.uid);
  const {data: businessAccount, refetch: refetchBusiness} = useGetBusinessAccount(auth().currentUser?.uid || '');

  // console.log('userAccount', userAccount);
  const isUserIsAttendee = eventAttendees?.some(attendee => attendee.user?.uid === userAccount?.uid) || false;

  const item = fetchedItem || itemFromRoute;
  const user = !(userAccount as unknown as {detail: string})?.detail ? userAccount : businessAccount;

  const publicDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];

  const userDomain = user?.email?.split('@')[1];
  const isPublicDomain = publicDomains.includes(userDomain || '');

  const [eventCountry, setEventCountry] = useState('');
  const [pincodeDialogVisible, setPinCodeDialog] = useState(false);
  const [postCode, setPostCode] = useState('');
  // console.log('user', user);

  const {data: hostUser} = useGetUserAccount(item?.host_id || '');
  const {data: hostBusiness} = useGetBusinessAccount(item?.host_id || '');
  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {mutateAsync: deleteEventUserStatus} = useDeleteUserEventStatus();
  const {mutateAsync: updateUserMutation} = useUpdateUserPostCode();

  const [isLoading, setIsLoading] = useState(false);
  const [spinner, setIsSpinner] = useState(false);
  const [isAnimationVisible, setAnimationVisisble] = useState(false);
  const {createShortUrl, isLoading: shareEventLoading} = useCreateShortUrl();

  const {data: categoryData} = useGetEventSubcategories(eventId);
  const {setSubCategory} = useEventCreationStore();
  const isEventPassed = moment(item?.end_date).isBefore(moment());
  const [randomUserImg1, serRandomUserImg1] = useState<any>();
  const [randomUserImg2, serRandomUserImg2] = useState<any>();

  useEffect(() => {
    serRandomUserImg1(getRandomItem(randomUserArray1));
    serRandomUserImg2(getRandomItem(randomUserArray2));
  }, []);

  const isFocused = useIsFocused();

  const [coords, setCoords] = useState({
    latitude: item ? item.coords.lat : 51.5072178, // Default latitude (e.g., London)
    longitude: item ? item.coords.long : -0.1275862, // Default longitude (e.g., London)
  });
  const [address, setAddress] = useState('Fetching address...');
  const [region, setRegion] = useState({
    latitude: 51.5072178,
    longitude: -0.1275862,
    latitudeDelta: 1,
    longitudeDelta: 1,
  });

  useEffect(() => {
    if (item?.coords?.lat && item?.coords?.long) {
      const newCoords = {
        latitude: item.coords.lat,
        longitude: item.coords.long,
      };
      setCoords(newCoords);
      setRegion(prev => ({
        ...prev,
        latitude: newCoords.latitude,
        longitude: newCoords.longitude,
      }));
      if (item.coord_address) {
        setAddress(item.coord_address);
      }
    }
  }, [item]);

  useEffect(() => {
    // Fetch address using reverse geocoding whenever coordinates change
    const fetchAddress = async () => {
      try {
        const apiKey = Config.GOOGLE_API_KEY; // Get the API key from your config
        const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coords.latitude},${coords.longitude}&key=${apiKey}&language=en`;

        // Perform the GET request using axios
        const response = await axios.get(url);

        // Check if we got valid results
        if (response.data.results && response.data.results.length > 0) {
          setAddress(response.data.results[0].formatted_address); // Set the address from the first result
        } else {
          setAddress('Address not found');
        }
      } catch (error) {
        // Handle any errors (e.g., network errors, invalid response, etc.)
        console.error('Error fetching address:', error);
        setAddress('Error fetching address');
      }
    };

    fetchAddress();
  }, [coords]);

  useEffect(() => {
    if (item?.coords?.lat && item?.coords?.long) {
      const newCoords = {
        latitude: item.coords.lat,
        longitude: item.coords.long,
      };
      setCoords(newCoords);
      if (item.coord_address) {
        setAddress(item.coord_address);
      }
    }
  }, [item]);

  useEffect(() => {
    if (isFocused) {
      refetchMatchinEventSatus();
      refetchAttendees();
      refetchEvent();
      refetch();
      refetchUser();
      refetchBusiness();
    }
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      request(PERMISSIONS.ANDROID.READ_CALENDAR).then(result => {
        console.log(`android calendar read permission: ${result}`);
      });
    }
  }, []);

  const getCountryFromLatLng = async () => {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${fetchedItem?.coords.lat},${fetchedItem?.coords.long}&key=AIzaSyAQMLxSRIygFhqS9dazo2HlJXs3rLrld-Q`,
    );

    const result = response.data.results[0];
    const country = result.address_components.find((component: any) => component.types.includes('country')).long_name;
    setEventCountry(country);
  };

  useEffect(() => {
    if (fetchedItem?.coords.lat) {
      getCountryFromLatLng();
    }
  }, [fetchedItem?.coords.lat]);

  const onNotificationAboutSubscriptionOn = async (item: any, host: any, responseData: any) => {
    setTimeout(async () => {
      if (responseData.status === USER_EVENT_STATUS.PENDING) {
        Notifier.showNotification({
          title: 'Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
        return;
      } else if (responseData.status === 'waiting') {
        Notifier.showNotification({
          title: 'You are in waitlist. Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
      } else {
        Notifier.showNotification({
          title: 'All Set! You are Ready for the event.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'success',
          },
        });
        refetchAttendees();
        if (item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
          setAnimationVisisble(true);
        }
      }
    }, 2000);
  };

  const onNotificationAboutSubscriptionOff = async (item: any, host: any) => {
    setTimeout(async () => {
      console.log('Notification Off'); // Додано логування
      Notifier.showNotification({
        title: 'We notified the organiser you are no longer going to the event.',
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      try {
        console.log('Fetching event owner data for host UID:', host.uid); // Додано логування
        // Отримуємо FCM token власника події
        const eventOwnerDoc = await firestore().collection('users').doc(host.uid).get();
        const eventOwnerData = eventOwnerDoc.data();
        console.log('Fetched event owner data:', eventOwnerData); // Додано логування
        if (eventOwnerData && eventOwnerData.deviceFcmToken) {
          console.log('Sending push notification to:', eventOwnerData.deviceFcmToken); // Додано логування
          // Відправляємо сповіщення
          await FirebaseChatsService.sendPushNotification(
            eventOwnerData.deviceFcmToken,
            'New Event Notification',
            `User ${userAccount?.first_name} are no longer going to the event ${item?.name}`,
            {
              clickAction: 'OPEN_NOTIFICATIONS',
              eventId: item.event_id,
              item: JSON.stringify(item), // Передаємо дані item у сповіщенні
            },
          );
        } else {
          console.log('Host does not have an FCM token.');
        }
      } catch (error) {
        console.error('Error fetching event owner data:', error);
      }
    }, 2000);
  };
  // console.log('fetchedItem', fetchedItem);

  const host = !(hostUser as unknown as {detail: string})?.detail ? hostUser : hostBusiness;
  // console.log('host', host);
  const isPyxiSelect = host?.uid === 'CmCCvWvpRVhhFlHdgU9QE6hbun82';

  const {setIsTabBarDisabled} = useTabBar();
  const mapRef = React.useRef<MapView>(null);
  const goBackHandler = () => {
    setIsTabBarDisabled(false);
    navigation.goBack();
  };

  React.useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    logScreenView('Event Detail', 'EventDetail');
  }, []);

  const isUserEvent = item?.host_id === auth().currentUser?.uid;

  const handleEditEvent = () => {
    if (!item) {
      return;
    }
    if (categoryData) {
      categoryData.forEach(cate_id => {
        setSubCategory(cate_id.subcategory_id);
      });
    }
    navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE, {item});
  };

  const submitDelete = () =>
    Alert.alert('Are you sure you want to delete this event?', '', [
      {
        text: 'No',
        style: 'cancel',
      },
      {text: 'Yes', onPress: () => handleDeleteSubmitted(), style: 'destructive'},
    ]);

  const handleDeleteSubmitted = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await deleteEvent({event_id: item.event_id});
    try {
      await FirebaseChatsService.deleteGroupChat({event_id: item.event_id});
    } catch (error) {}
    navigation.goBack();
  };

  const cancelEvent = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await cancelAEvent({event_id: item.event_id});
    refetchEvent();
    setIsLoading(false);
  };

  const userName = (user as User)?.last_name
    ? (user as User)?.first_name + ' ' + (user as User)?.last_name
    : (user as User)?.first_name || (user as Business)?.name;

  const handleSharePress = async () => {
    if (!item?.event_id) {
      return;
    }
    const redirectLink = `https://partner.pyxi.ai/event/detail/${item.event_id}`;
    const url = redirectLink;
    await Share.open({
      message: t('invite_message', {userName, itemName: item.name, url}),
    });
  };

  const handleOpenMaps = () => {
    if (!item?.coords?.lat || !item?.coords?.long) {
      return;
    }
    openLocationInMaps({lat: item.coords.lat, long: item.coords.long, pointLabel: item?.address_name});
  };

  const onTextLayout = React.useCallback((e: NativeSyntheticEvent<TextLayoutEventData>) => {
    //to check should truncate or not
    setLengthMore(e.nativeEvent.lines.length >= TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      refetchEvent();
      refetchAttendees(); // Додаємо рефетч для учасників
    }, [refetchEvent, refetchAttendees]),
  );

  const onDatePress = () => {
    calendarRef.current?.open();
  };

  const {data: groups} = useQuery<Group[]>('joined-groups', async () => {
    const token = await FirebaseAuth.getAuthToken();
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    const response = await axios.get(Config.BASE_API_URL + 'community/joined', config);
    return response.data;
  });

  const handleMatchingPress = () => {
    if (false && userStatus?.status != USER_EVENT_STATUS.ACCEPTED) {
      Alert.alert('Note!', 'Please join the event before searching for someone to go with.');
      return;
    }
    if (matchingStatus?.matches_available && matchingStatus?.type === 'colleague') {
      Alert.alert(
        'Note!',
        "If you find someone to go with, your current colleague matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
        [
          {
            text: 'No',
            onPress: () => {
              // If user cancels, close the dialog and do nothing
            },
            style: 'cancel',
          },
          {
            text: 'Yes',
            onPress: () => {
              setCurrentMatchingEvent(item?.event_id || null);
              setRefresh(true);
              openMatchingLoadingModal();
            },
          },
        ],
      );
    } else {
      // Check if user has groups before showing bottom sheet
      if (groups && groups.length > 0) {
        matchingOptionsSheetRef.current?.present();
      } else {
        // If no groups, go directly to matching with 'anyone' type
        setCurrentMatchingEvent(item?.event_id || null);
        setDomain(userDomain || null);
        setMatchingType('anyone');
        setRefresh(false);
        openMatchingLoadingModal();
      }
    }
  };

  if (!itemFromRoute && isEventLoading) {
    return <EventDetailsPlaceholder />;
  }
  // if (!itemFromRoute && isEventLoading) {
  //   return <EventDetailsPlaceholder />;
  // }

  const addToCalendar = () => {
    if (Platform.OS == 'ios') {
      addEventToCalendar(item?.name, item?.start_date, item?.end_date, item?.description, item?.address_name);
    } else {
      const eventConfig = {
        title: item?.name,
        startDate: new Date(item?.start_date || '').toISOString(),
        endDate: new Date(item?.end_date || '').toISOString(),
        location: item?.address_name,
        notes: item?.description,
      };

      addCalendarEvent(eventConfig);
    }
  };

  const addJoinEventStatus = async () => {
    setModalIsVisible(false);
    await addEventUserStatus({
      event_id: eventId.toString(),
    });
  };

  const onJoinEventClick = () => {
    if (isWithin12HoursOfStart(item?.start_date, isPyxiSelect) && !item?.event_group_id) {
      Alert.alert('Event Unavailable', 'This event is no longer accepting registrations as it starts within 12 hours.');
      return;
    }

    if (!userStatus && eventCountry === 'Greece') {
      if (userAccount?.postal_code) {
        joinEvent();
      } else {
        setPinCodeDialog(true);
      }
    } else {
      joinEvent();
    }
  };

  const onJoinEvent = async (event_id: number, isLeave: boolean) => {
    if (isWithin12HoursOfStart(item?.start_date, isPyxiSelect)) {
      Alert.alert('Event Unavailable', 'This event is no longer accepting registrations as it starts within 12 hours.');
      return;
    }
    setIsSpinner(true);
    await joinEvent(event_id, isLeave);
    setIsSpinner(false);
  };

  const onAddComment = async (comment: string) => {
    const reqBody = {
      comment: comment,
      pin: false,
      parent_id: null,
      event_id: item?.event_id || 0,
    };
    await createComment(reqBody);
    refetchCommentEvent();
  };

  const joinEvent = async (event_id?: number, isLeave?: boolean) => {
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: true});
      return;
    }
    if (item?.event_group_id && !event_id) {
      onDatePress();
      return;
    }
    if (item && !item?.payment_url) {
      if (userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
        navigation.navigate(SCREENS.BUY_TICKET, {eventId: item.event_id});
        return;
      } else {
        navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus.order_id + ''});
        return;
      }
    }

    if (item?.payment_url && userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
      setModalIsVisible(true);
    }

    if (!item) {
      return;
    }
    if (event_id) {
      if (!isLeave) {
        const data = await addEventUserStatus({
          event_id: event_id.toString(),
        });
        if (!item?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(item, host)
            : onNotificationAboutSubscriptionOn(item, host, data);
        }

        if (item.event_type === 'business' || item.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event_id,
          event_image: item.image_url,
        });
      }
    } else {
      if (!userStatus) {
        const data = await addEventUserStatus({
          event_id: eventId.toString(),
        });
        if (!item?.is_paid) {
          // @ts-ignore
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(item, host)
            : onNotificationAboutSubscriptionOn(item, host, data);
        }

        if (item.event_type === 'business' || item.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: item.event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: item.event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: item.event_id,
          event_image: item.image_url,
        });
      }
    }
    if (event_id) {
      if (event_id !== item.event_id) {
        navigation.replace(SCREENS.HOME_EVENT, {eventId: event_id});
      } else {
        await refetch();
      }
    } else {
      await refetch();
    }
  };
  const onContinuePress = async () => {
    if (!postCode && postCode.length < 3) {
      Alert.alert('Please enter postal code.');
    }

    setPinCodeDialog(false);

    const data = await updateUserMutation(postCode);
    if (data.postal_code) {
      joinEvent();
    }
  };
  const onCommentPress = () => {
    commentSheetRef.current?.present();
  };
  const getAttendeesCount = () => {
    let totalAttendees = 1;
    if (eventAttendees && eventAttendees.length > 0) {
      totalAttendees = 1 + eventAttendees.length;
    }
    if (totalAttendees > 2) {
      totalAttendees = totalAttendees - 2;
    }
    return totalAttendees;
  };
  const getLinkOf1stAttendeesProfile = () => {
    if (eventAttendees && eventAttendees.length > 0 && eventAttendees[0].user && eventAttendees[0].user.photo) {
      return eventAttendees[0].user.photo;
    }
    return null;
  };
  const getLinkOf2ndAttendeesProfile = () => {
    if (eventAttendees && eventAttendees.length > 1 && eventAttendees[1].user && eventAttendees[1].user.photo) {
      return eventAttendees[1].user.photo;
    }
    return null;
  };
  const onQrPress = () => {
    navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus?.order_id + ''});
  };
  const onHostChatClick = async (host: Business) => {
    setModalVisible(true);
  };

  const onIssueTypeClick = async (type: string) => {
    const hHost = type == 't' ? pyxiHost : host;
    if (hHost && item) {
      const chatId = await FirebaseChatsService.createOrganisationChat({
        user_id1: auth().currentUser!.uid,
        user_id2: hHost?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: (hHost as Business)?.name
          ? (hHost as Business)?.name
          : (hHost as unknown as User)?.last_name
            ? `${(hHost as unknown as User)?.first_name} ${(hHost as unknown as User)?.last_name || ''}`
            : (hHost as unknown as User)?.first_name || '',
        user_image: userAccount?.photo + '',
        event: item,
        isTechnical: type == 't' ? true : false,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
    }
  };

  return (
    <BottomSheetModalProvider>
      {spinner && (
        <View style={styles.spinnerView}>
          <ActivityIndicator size={'large'} color={'#F5A865'} />
        </View>
      )}
      <GoBackHeader customCallback={goBackHandler} />
      {isUserEvent && false && (
        <Button
          label={t('events.delete_event')}
          isLoading={isLoading}
          onPress={() => submitDelete()}
          textStyle={{color: '#fff'}}
          containerStyle={{
            position: 'absolute',
            right: 16,
            top: isAndroid ? top + 30 : top,
            zIndex: 1000,
            backgroundColor: 'red',
            paddingVertical: 8,
          }}
        />
      )}

      <ScrollView style={detailStyles.background} bounces={false}>
        <Animated.View style={detailStyles.imageWrapper}>
          <FastImage
            style={StyleSheet.absoluteFillObject}
            resizeMode={'cover'}
            source={{
              uri: item?.image_url,
              priority: 'high',
            }}
          />
          <Animated.Image sharedTransitionTag={tag} source={{uri: item?.image_url}} style={detailStyles.image} />
          {!isUserEvent && (
            <View style={styles.likeContainer}>
              <LikeButton liked={!!item?.user_liked} eventId={item?.event_id} />
            </View>
          )}

          {isUserEvent &&
            item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2' &&
            item?.event_type === 'business' &&
            false && <ScannerQRCode user={eventAttendees} event={fetchedItem} />}
          {!isUserEvent &&
            isUserIsAttendee &&
            item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2' &&
            item?.event_type === 'business' && (
              <View style={styles.likeContainer}>
                <QRCodeGenerator onQrPress={onQrPress} user={userAccount} event={fetchedItem} />
              </View>
            )}
        </Animated.View>
        <View style={detailStyles.container}>
          <View>
            <View style={[detailStyles.priceWrapper, detailStyles.margin]}>
              <Animated.Text style={detailStyles.header} entering={FadeInDown.delay(200)}>
                {item?.name}
              </Animated.Text>
            </View>
            <Pressable
              onPress={() =>
                setDescriptionNumberOfLines(prevState => (prevState ? 0 : TRUNCATE_DESCRIPTION_NUMBER_OF_LINES))
              }>
              <Hyperlink
                onPress={url => Linking.openURL(url)}
                linkStyle={[detailStyles.description, detailStyles.linkColor]}>
                <Animated.Text
                  onTextLayout={onTextLayout}
                  style={detailStyles.description}
                  entering={FadeInDown.delay(300)}
                  numberOfLines={descriptionNumberOfLines}>
                  {item?.description}
                </Animated.Text>
              </Hyperlink>
              {lengthMore ? (
                <Text style={[detailStyles.description, detailStyles.readMoreTest]}>
                  {descriptionNumberOfLines ? 'Read more...' : 'Read less...'}
                </Text>
              ) : null}
            </Pressable>
          </View>
          {((commentList && commentList?.length > 0) || isUserEvent) && <View style={styles.divider} />}
          {commentList && commentList?.length > 0 ? (
            <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
              <Text style={styles.commentTitle}>Updates {commentList.length}</Text>
              <View style={styles.commentItemView}>
                <Image source={{uri: commentList[0].user.photo}} style={styles.userImage} />
                <Text style={styles.commentTextView}>{commentList[commentList.length - 1].comment}</Text>
              </View>
            </TouchableOpacity>
          ) : (
            isUserEvent && (
              <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
                <Text style={styles.commentTitle}>Updates</Text>
                <View style={[styles.commentItemView, {alignItems: 'center'}]}>
                  <Image source={{uri: user?.photo}} style={styles.userImage} />
                  <Text style={styles.commentTextView}>Add updates here</Text>
                </View>
              </TouchableOpacity>
            )
          )}
          {((commentList && commentList?.length > 0) || isUserEvent) && <View style={styles.divider} />}
          <View style={{flexDirection: 'row', flex: 1, justifyContent: 'space-between'}}>
            <View style={{flex: 1}}>
              <View style={[styles.checkContainer, {flex: 1}]}>
                <CheckIcon />
                <View style={styles.checkRightContainer}>
                  <Text style={styles.checkLabel}>Date & Time</Text>
                  <View style={{flex: 1}}>
                    <Text style={styles.checkDescription}>
                      {item?.start_date ? moment.utc(item?.start_date).format(DATE_FORMAT) : 'N/A'}
                      {item?.end_date ? ' -' : ''}
                    </Text>
                    {item?.end_date && (
                      <Text style={styles.checkDescription}>{moment.utc(item?.end_date).format(DATE_FORMAT)}</Text>
                    )}
                  </View>
                </View>
              </View>

              <TouchableOpacity style={{...styles.checkContainer, marginTop: 16}} onPress={handleOpenMaps}>
                <CheckIcon />
                <View style={styles.checkRightContainer}>
                  <Text style={styles.checkLabel}>{'Location'}</Text>
                  <Text style={styles.checkDescription}>{item?.address_name || item?.coord_address}</Text>
                </View>
              </TouchableOpacity>

              {item?.event_group_id && (
                <View style={{...styles.checkContainer, marginTop: 16}}>
                  <CheckIcon />
                  <View style={styles.checkRightContainer}>
                    <Text style={styles.checkLabel}>{'Recurrence'}</Text>
                    <Text style={styles.checkDescription}>{getMessageOfRecurrence(item?.event_group)}</Text>
                  </View>
                </View>
              )}
            </View>
            {item?.host_id !== auth().currentUser?.uid && !userStatusIsLoading && !userStatusIsFetching ? (
              <View
                key={userStatus?.status || 'statusButton'}
                // sharedTransitionTag={statusTag}
                style={{aspectRatio: 1, width: 100, alignSelf: 'center'}}>
                {!isEventPassed || userStatus?.status ? (
                  <Button
                    disabled={
                      userStatus?.status === USER_EVENT_STATUS.PENDING ||
                      item?.is_cancelled ||
                      moment(item?.end_date).isBefore(moment()) ||
                      (isWithin12HoursOfStart(item?.start_date, isPyxiSelect) && !item?.event_group_id)
                    }
                    icon={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED ? (
                        <BigCheckIcon />
                      ) : userStatus?.status === USER_EVENT_STATUS.PENDING ? (
                        <BigPendingIcon />
                      ) : undefined
                    }
                    label={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                        ? 'Accepted'
                        : userStatus?.status === USER_EVENT_STATUS.PENDING
                          ? 'Pending'
                          : userStatus?.status === 'waiting'
                            ? 'Waitlisted'
                            : isWithin12HoursOfStart(item?.start_date, isPyxiSelect) && !item?.event_group_id
                              ? 'Sold Out'
                              : item?.is_paid || item?.payment_url
                                ? 'Buy tickets'
                                : 'Join'
                    }
                    containerStyle={{
                      backgroundColor: item?.is_cancelled
                        ? '#8E8E93'
                        : userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                          ? '#34C759'
                          : userStatus?.status === USER_EVENT_STATUS.PENDING
                            ? '#FF9500'
                            : userStatus?.status === 'waiting'
                              ? '#FF9500'
                              : isWithin12HoursOfStart(item?.start_date, isPyxiSelect) && !item?.event_group_id
                                ? '#BCBCBC'
                                : '#2E2EAB',
                      marginTop: 12,
                      flex: 1,
                      borderRadius: 20,
                      paddingVertical: undefined,
                    }}
                    textStyle={{
                      fontSize: userStatus?.status === 'waiting' ? 12 : 16,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: '#fff',
                      lineHeight: 20,
                    }}
                    onPress={async () => onJoinEventClick()}
                  />
                ) : (
                  <Button
                    label={'Buy tickets'}
                    containerStyle={{
                      backgroundColor: 'gray',
                      marginTop: 12,
                      flex: 1,
                      borderRadius: 20,
                      paddingVertical: undefined,
                    }}
                    textStyle={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: '#fff',
                      lineHeight: 20,
                    }}
                    onPress={async () => Alert.alert('The event has ended')}
                  />
                )}
              </View>
            ) : (
              (userStatusIsLoading || userStatusIsFetching) && (
                <View style={styles.loaderJoinBtnView}>
                  <ActivityIndicator />
                </View>
              )
            )}
          </View>

          <View style={styles.divider} />

          {!fetchedItem ? (
            <></>
          ) : fetchedItem?.is_cancelled ? (
            <>
              <Text style={styles.errorText}>
                {fetchedItem?.event_type === 'business'
                  ? 'This event was cancelled by the organiser'
                  : 'This event was cancelled by host.'}
              </Text>
              {isUserEvent && (
                <Button
                  label={t('events.update_event')}
                  containerStyle={{backgroundColor: '#FF9500', marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                  onPress={handleEditEvent}
                />
              )}
            </>
          ) : (
            <>
              {isUserEvent ? (
                <Button
                  label={t('events.update_event')}
                  containerStyle={{backgroundColor: '#FF9500'}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                  onPress={handleEditEvent}
                />
              ) : (
                <View style={{flexDirection: 'column', justifyContent: 'space-between', marginTop: 10}}>
                  <Button
                    label={
                      matchingStatus?.matches_available
                        ? matchingStatus?.type === 'all'
                          ? t('events.see_my_match')
                          : t('home.find_someone')
                        : t('home.find_someone')
                    }
                    disabled={isPyxiSelect}
                    containerStyle={{
                      backgroundColor: isPyxiSelect
                        ? '#AEAEAE'
                        : matchingStatus?.matches_available
                          ? '#34C759'
                          : '#2E2EAB',
                    }}
                    textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                    onPress={handleMatchingPress}
                  />
                  {/* Render the second button only if the domain is not public */}
                  {(host as Business) && (host as Business)!.user_pooling! && (
                    <Button
                      label={
                        matchingStatus?.matches_available
                          ? matchingStatus?.type === 'colleague'
                            ? 'See My Colleagues'
                            : 'Find My Colleagues'
                          : 'Find My Colleagues'
                      }
                      containerStyle={{
                        backgroundColor: '#FF9500',
                        marginTop: 10,
                      }}
                      textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                      onPress={() => {
                        if (matchingStatus?.matches_available && matchingStatus?.type === 'all') {
                          // Show the confirmation dialog
                          Alert.alert(
                            'Note!',
                            "If you find my colleagues, your current matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
                            [
                              {
                                text: 'No',
                                onPress: () => {
                                  // If user cancels, close the dialog and do nothing
                                },
                                style: 'cancel',
                              },
                              {
                                text: 'Yes',
                                onPress: () => {
                                  // If user confirms, proceed with the action
                                  setCurrentMatchingEvent(item?.event_id || null);
                                  setRefresh(true);
                                  openMatchingLoadingModal();
                                },
                              },
                            ],
                          );
                        } else {
                          // If the condition is not 'all', proceed without dialog
                          matchingOptionsSheetRef.current?.present();
                        }
                      }}
                    />
                  )}
                </View>
              )}

              {isUserEvent && false && (
                <Button
                  label={t('events.cancel_event')}
                  containerStyle={{backgroundColor: '#FF9500', marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                  onPress={() => cancelEvent()}
                  isLoading={isLoading}
                />
              )}

              <Button
                label={t('home.share')}
                containerStyle={{
                  backgroundColor: 'white',
                  marginTop: 12,
                  flex: 1,
                  width: undefined,
                  paddingVertical: 10,
                  borderWidth: 1,
                  borderColor: '#2E2EAB',
                }}
                spinnerColor={'#2E2EAB'}
                isLoading={shareEventLoading}
                textStyle={{fontWeight: '500', color: '#2E2EAB'}}
                onPress={handleSharePress}
              />

              <Button
                label="Add to my calendar"
                containerStyle={{backgroundColor: '#FF9500', marginTop: 10}}
                textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
                onPress={() => addToCalendar()}
              />
            </>
          )}

          <View style={styles.divider} />

          {!!host && (
            <>
              <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
                <Text style={{fontSize: 18, lineHeight: 20, fontWeight: '600'}}>{t('events.host')}</Text>
                {isUserEvent && (
                  <Button
                    onPress={() =>
                      navigation.navigate(SCREENS.PENDING_ATTENDEES, {eventId: item!.event_id, eventName: item!.name})
                    }
                    label={t('events.requiring_confirmation')}
                    containerStyle={{
                      backgroundColor: '#2E2EAB',
                      borderRadius: 50,
                    }}
                    textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: '#fff'}}
                  />
                )}
              </View>
              <View style={{marginTop: 20}}>
                <TouchableOpacity
                  style={styles.attendeeContainer}
                  onPress={() => {
                    navigation.navigate(SCREENS.PERSONAL_INFO, {
                      user: {
                        tag: host?.uid + 'image',
                        source: host?.photo || '',
                        description: host?.description || '',
                        name: (host as Business)?.name
                          ? (host as Business)?.name
                          : (host as User)?.last_name
                            ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                            : (host as User)?.first_name || '',
                        user_id: host?.uid || '',
                        eventName: fetchedItem?.name,
                      },
                    });
                  }}>
                  <View style={styles.attendeeLeftContainer}>
                    <View style={styles.attendeePhoto}>
                      <FastImage
                        style={StyleSheet.absoluteFillObject}
                        resizeMode={'cover'}
                        source={{uri: host?.photo, priority: 'normal'}}
                      />
                      <Animated.Image
                        sharedTransitionTag={host?.uid + 'image'}
                        source={{uri: host?.photo}}
                        style={styles.attendeePhoto}
                      />
                    </View>

                    <Text style={styles.attendeeName}>
                      {(host as Business)?.name
                        ? (host as Business)?.name
                        : (host as User)?.last_name
                          ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                          : (host as User)?.first_name || ''}
                    </Text>
                    {/* !isUserEvent ? (
                      <View style={styles.textRowContainer}>
                        <Text>Verified Organiser</Text>
                        <Image
                          source={require('../../../assets/images/verified-badge.png')}
                          style={styles.verifiedIcon}
                        />
                      </View>
                    ) : (
                      <View />
                    ) */}
                    {!isUserEvent && (
                      <Button
                        onPress={() => onHostChatClick(host as Business)}
                        label={t('chat.chat')}
                        containerStyle={{
                          backgroundColor: '#2E2EAB',
                          borderRadius: 50,
                        }}
                        textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: '#fff'}}
                      />
                    )}
                  </View>
                </TouchableOpacity>
                {<View style={styles.divider} />}
              </View>
            </>
          )}

          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
            <Text style={{fontSize: 18, lineHeight: 20, fontWeight: '600'}}>{t('events.attendees')}</Text>
            {false && isUserEvent && eventAttendees && eventAttendees!.length >= 1 && (
              <Button
                onPress={() => {
                  navigation.navigate(SCREENS.CHAT_STACK, {});
                }}
                label={t('chat.chat_all')}
                containerStyle={{
                  backgroundColor: '#2E2EAB',
                  borderRadius: 50,
                }}
                textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: '#fff'}}
              />
            )}
          </View>
          <View style={{marginTop: 20, marginBottom: 24}}>
            {item?.host_id != user?.uid && (
              <View style={styles.attendeeRowStyle}>
                <Image
                  style={styles.profileImg}
                  source={getLinkOf1stAttendeesProfile() ? {uri: getLinkOf1stAttendeesProfile()} : randomUserImg1}
                />
                <Image
                  style={[styles.profileImg, {left: -15}]}
                  source={getLinkOf2ndAttendeesProfile() ? {uri: getLinkOf2ndAttendeesProfile()} : randomUserImg2}
                />
                <Text style={styles.attendeesCount}>+ {getAttendeesCount()}</Text>
              </View>
            )}
            {item?.host_id === user?.uid && (
              <>
                {eventAttendees ? (
                  eventAttendees.map?.((attendee, index) => (
                    <React.Fragment key={index}>
                      <TouchableOpacity
                        style={styles.attendeeContainer}
                        disabled={!attendee.user}
                        onPress={() => {
                          navigation.navigate(SCREENS.PERSONAL_INFO, {
                            user: {
                              tag: index + 'image',
                              source: attendee.user.photo,
                              description: attendee.user.description,
                              name: `${attendee.user.first_name} ${attendee.user.last_name || ''}`,
                              user_id: attendee.user.uid,
                              eventName: fetchedItem?.name,
                            },
                          });
                        }}>
                        <View style={styles.attendeeLeftContainer}>
                          {attendee?.user && (
                            <View style={styles.attendeePhoto}>
                              <FastImage
                                style={StyleSheet.absoluteFillObject}
                                resizeMode={'cover'}
                                source={{uri: attendee?.user?.photo, priority: 'normal'}}
                              />
                              <Animated.Image
                                sharedTransitionTag={index + 'image'}
                                source={{uri: attendee?.user?.photo}}
                                style={styles.attendeePhoto}
                              />
                            </View>
                          )}

                          {attendee?.user ? (
                            <Text
                              style={
                                styles.attendeeName
                              }>{`${attendee?.user?.first_name} ${attendee?.user?.last_name}`}</Text>
                          ) : (
                            <Text style={[styles.attendeeName, {marginLeft: 0}]}>
                              {`${attendee?.name}`} <Text style={styles.attendeeSub}>{`(external)`}</Text>
                            </Text>
                          )}
                        </View>
                        {isUserEvent && (
                          <Button
                            label={t('generic.remove')}
                            onPress={async () => {
                              await deleteEventUserStatus({
                                event_id: eventId,
                                user_id: attendee.user?.uid,
                                email: attendee.email,
                              });
                            }}
                            containerStyle={styles.buttonRejectContainer}
                            textStyle={styles.buttonText}
                          />
                        )}
                      </TouchableOpacity>
                      {<View style={styles.divider} />}
                    </React.Fragment>
                  ))
                ) : (
                  <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item width={'100%'}>
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                    </SkeletonPlaceholder.Item>
                  </SkeletonPlaceholder>
                )}
              </>
            )}
          </View>
        </View>
        <MapView
          ref={mapRef}
          style={{
            flex: 1,
            minHeight: 300,
            maxHeight: height * 0.85,
            marginHorizontal: 16,
            borderRadius: 16,
            marginBottom: 60,
          }}
          provider={PROVIDER_GOOGLE}
          initialRegion={{
            latitude: item?.coords?.lat || 51.5072178,
            longitude: item?.coords?.long || -0.1275862,
            latitudeDelta: 0.015,
            longitudeDelta: 0.015,
          }}
          showsUserLocation={true}
          scrollEnabled={true}
          zoomEnabled={true}
          pitchEnabled={true}
          rotateEnabled={true}>
          {Platform.OS == 'android' && (
            <Marker coordinate={coords}>
              <View style={styles.customMarkerContainer}>
                <View style={styles.markerContent}>
                  <Text style={styles.addressText}>{address}</Text>
                </View>
                <View style={styles.arrowContainer}>
                  <View style={styles.arrow} />
                </View>
              </View>
            </Marker>
          )}
          {Platform.OS == 'ios' && (
            <Marker
              coordinate={{
                latitude: item?.coords?.lat || 51.5072178,
                longitude: item?.coords?.long || -0.1275862,
              }}>
              <Image
                source={require('../../../assets/images/pinEvent.png')}
                resizeMode="contain"
                style={{width: 35, height: 35, tintColor: '#3D3DE5'}} // Customize the size here
              />
            </Marker>
          )}
        </MapView>
      </ScrollView>
      <Calendar refRBSheet={calendarRef} recurrences={item?.recurrence_events || []} onJoinEvent={onJoinEvent} />

      <Dialog
        visible={pincodeDialogVisible}
        contentStyle={{padding: 0}}
        contentInsetAdjustmentBehavior="always"
        titleStyle={{
          alignSelf: 'flex-start',
          fontSize: 14,
          fontWeight: 'bold',
        }}
        title="Please enter post code"
        onTouchOutside={() => setPinCodeDialog(false)}
        onRequestClose={function (): void {}}>
        <View
          style={{
            paddingHorizontal: 15,
            paddingBottom: 15,
          }}>
          <TextInput
            placeholder="Enter post code"
            value={postCode}
            style={styles.textInput}
            onChangeText={setPostCode}
          />
          <Button
            label={'Continue'}
            containerStyle={{backgroundColor: '#F5A865', marginTop: 10}}
            textStyle={{color: 'white'}}
            onPress={onContinuePress}
          />
        </View>
      </Dialog>
      {
        <ModalWithJoin
          name={`${item?.name}`}
          description={`${item?.description}`}
          isVisible={modalIsVisible}
          close={() => setModalIsVisible(false)}
          payment_url={`${item?.payment_url}`}
          onPress={() => () => {
            addJoinEventStatus();
          }}
        />
      }
      <CommentSheet
        commentSheetRef={commentSheetRef}
        commentList={commentList ? commentList : []}
        onAddComment={onAddComment}
        photo={user?.photo}
        isUserEvent={isUserEvent}
      />
      {/* <ModalWithCalendar startDate={new Date(item?.start_date)} endDate={new Date(item?.end_date)} /> */}
      <AnimatedLoadingModal
        isVisible={isAnimationVisible}
        isButtonVisible={false}
        callback={() => setAnimationVisisble(false)}
        close={() => setAnimationVisisble(false)}
        isQRCode={true}
      />

      <IssueModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onTechnicalIssueClick={() => {
          console.log('Technical Issue Selected');
          onIssueTypeClick('t');
          setModalVisible(false);
        }}
        onEventIssueClick={() => {
          onIssueTypeClick('e');
          console.log('Event Issue Selected');
          setModalVisible(false);
        }}
      />

      <MatchingOptionsSheet
        bottomSheetRef={matchingOptionsSheetRef}
        onOptionSelect={type => {
          setCurrentMatchingEvent(item?.event_id || null);
          setDomain(null);
          setMatchingType(type);
          setRefresh(true);
          openMatchingLoadingModal();
        }}
      />
    </BottomSheetModalProvider>
  );
}
