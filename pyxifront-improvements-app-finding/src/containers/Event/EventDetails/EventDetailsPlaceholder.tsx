import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {View} from 'react-native';
import {styles} from './styles';

const EventDetailsPlaceholder = () => (
  <View style={styles.flex}>
    <SkeletonPlaceholder>
      <>
        <SkeletonPlaceholder.Item height={300} width="100%" borderRadius={10} />
        <SkeletonPlaceholder.Item paddingLeft={22} paddingRight={22} marginTop={10}>
          <SkeletonPlaceholder.Item height={40} width={200} borderRadius={10} marginTop={32} />
          <SkeletonPlaceholder.Item height={100} borderRadius={10} marginTop={24} />
          <View style={styles.divider} />
          <SkeletonPlaceholder.Item height={200} borderRadius={10} marginTop={8} />
          <View style={styles.divider} />
          <SkeletonPlaceholder.Item height={200} borderRadius={10} marginTop={8} />
        </SkeletonPlaceholder.Item>
      </>
    </SkeletonPlaceholder>
  </View>
);

export default EventDetailsPlaceholder;
