import {<PERSON><PERSON><PERSON>, SafeAreaView} from 'react-native';
import {GoBackHeader} from '~components/GoBackHeader';
import PersonItem from '~components/Matching/PersonItem';
import {useGetMatches} from '~hooks/event/useGetMatches';
import {useMatchingLoaderAnimationStore} from '~providers/matching/zustand';
import styles from './styles';
import {useEffect, useState} from 'react';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useRoute} from '@react-navigation/native';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {User} from '~types/api/user';
import Button from '~components/Button';
import {t} from 'i18next';

const MatchingUsersScreen = () => {
  const {matchingEventId, domain, refresh, setRefresh, matchingType} = useMatchingLoaderAnimationStore();

  const {data, refetch} = useGetMatches(matchingEventId, refresh, domain, matchingType);
  const route: any = useRoute();
  const {data: userAccount} = useGetUserAccount(auth().currentUser?.uid);

  // Extract event and domain from route parameters
  const {event} = route.params;

  useEffect(() => {
    logScreenView('Matching Users', 'MatchingUsersScreen');
    console.log('Domain:', domain); // Log the domain for debugging
  }, [domain]);

  const refreshAttendees = () => {
    setRefresh(true); // Toggle refresh to a new value
    setTimeout(() => {
      refetch();
    }, 300);
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <GoBackHeader />
      <FlatList
        data={data}
        style={styles.container}
        contentContainerStyle={styles.padding}
        keyExtractor={item => `${item.uid}=match`}
        renderItem={({item, index: i}) => (
          <PersonItem 
            person={item} 
            i={i} 
            event={event} 
            userAccount={userAccount as User} 
          />
        )}
      />
      <Button
        label={t('events.refresh')}
        containerStyle={{backgroundColor: '#FF9500', width: '90%', alignSelf: 'center', marginBottom: 10}}
        textStyle={{fontSize: 16, fontWeight: '600', color: '#fff'}}
        onPress={refreshAttendees}
      />
    </SafeAreaView>
  );
};

export default MatchingUsersScreen;
