import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {RouteProp, useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import {FlashList} from '@shopify/flash-list';
import moment from 'moment-timezone';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Image, ImageBackground, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {KeyboardGestureArea, useKeyboardHandler} from 'react-native-keyboard-controller';
import Animated, {useAnimatedStyle, useSharedValue} from 'react-native-reanimated';
import {addDateHeaders} from '~Utils/chat';
import {ChevronIcon, LogoIcon} from '~assets/icons';
import {ChatInput, ChatItem} from '~components/Chat';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import {CHAT_MESSAGE_TYPE_ENUM, ChatType, ChatTypeWithKey} from '~types/chat';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useChatStore} from '~providers/chats/zustand'; // Імпортуємо ваш Zustand store
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {Business} from '~types/api/business';
import {User} from '~types/api/user';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import LikeButton from '~components/LikeButton';

const useKeyboardAnimationAndroid = () => {
  const progress = useSharedValue(0);
  const height = useSharedValue(0);

  useKeyboardHandler({
    onMove: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
  });

  return {height, progress};
};

const useKeyboardAnimationIos = () => {
  const progress = useSharedValue(0);
  const height = useSharedValue(0);
  const shouldUseOnMoveHandler = useSharedValue(false);
  useKeyboardHandler({
    onStart: e => {
      'worklet';

      if (progress.value !== 1 && progress.value !== 0 && e.height !== 0) {
        shouldUseOnMoveHandler.value = true;
        return;
      }

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onMove: e => {
      'worklet';

      if (shouldUseOnMoveHandler.value) {
        progress.value = e.progress;
        height.value = e.height;
      }
    },
    onEnd: e => {
      'worklet';

      height.value = e.height;
      progress.value = e.progress;
      shouldUseOnMoveHandler.value = false;
    },
  });

  return {height, progress};
};

const AnimatedFlatList = Animated.createAnimatedComponent(FlashList);

const useKeyboardAnimation = Platform.OS === 'android' ? useKeyboardAnimationAndroid : useKeyboardAnimationIos;

const ChatWithUsers = () => {
  const {setIsTabBarDisabled} = useTabBar();
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.USER_CHAT>>();
  const {data: currentUser} = useGetUserAccount(auth().currentUser!.uid);
  const {image, userName} = params;
  const {data: host} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const {isAcceptedChats, acceptChat, checkChatAcceptance} = useChatStore(state => ({
    isAcceptedChats: state.isAcceptedChats,
    acceptChat: state.acceptChat,
    checkChatAcceptance: state.checkChatAcceptance,
  }));

  const defaultCallback = () => {
    navigation.goBack();
  };

  useFocusEffect(
    useCallback(() => {
      setIsTabBarDisabled(true);
    }, []),
  );

  useEffect(() => {
    logScreenView('Chat With Users', 'ChatWithUsers');
  }, []);

  const key = params?.chatId;

  const [currentChat, setCurrentChat] = useState<ChatTypeWithKey | null>(null);
  const [isAccepted, setIsAccepted] = useState<boolean | null>(null);
  const [isInitiator, setIsInitiator] = useState(false);

  const userIndex = currentChat?.userIds.findIndex(
    user => user.toLowerCase() !== (auth().currentUser!.uid || '').toLowerCase(),
  );
  const {data: otherUser, refetch: refetchUserAccount} = useGetUserAccount(currentChat?.userIds[userIndex || 0], false);
  const {data: otherBusinessUser, refetch: refetchBusinessAccount} = useGetBusinessAccount(
    currentChat?.userIds[userIndex || 0] || '',
    false,
  );

  useEffect(() => {
    refetchBusinessAccount().then(data => {
      if (!data.data) {
        refetchUserAccount();
      }
    });
  }, [currentChat]);

  const isConfirmationRequired = useMemo(() => {
    if (currentChat && currentChat.history && currentChat.history.length > 1) {
      const isConfirmMessage = currentChat.history[0];
      if (isConfirmMessage.type == CHAT_MESSAGE_TYPE_ENUM.ISSUE_CONFIRMATION) {
        return true;
      }
      return false;
    }
    return false;
  }, [currentChat]);

  const {height, progress} = useKeyboardAnimation();

  const flatListRef = useRef<any>(null);

  const scrollViewStyle = useAnimatedStyle(
    () => ({
      flex: 1,
      marginBottom: height.value,
    }),
    [],
  );

  useEffect(() => {
    const checkAcceptance = async () => {
      const accepted = await checkChatAcceptance(key);
      setIsAccepted(accepted);
    };

    checkAcceptance();

    const chatSubscribe = firestore()
      .collection('chats')
      .doc(key)
      .onSnapshot(documentSnapshot => {
        const data = documentSnapshot.data() as ChatTypeWithKey | undefined;
        if (data) {
          const sortedMessages = [
            ...data.history.sort((a, b) => {
              return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
            }),
          ];

          setCurrentChat({
            ...data,
            history: addDateHeaders(sortedMessages).reverse(),
            key: documentSnapshot.id,
          } as ChatTypeWithKey);

          // Перевірка ініціатора чату за першим користувачем в масиві users
          const isCurrentUserInitiator = data.userIds[0] === auth().currentUser!.uid;
          setIsInitiator(isCurrentUserInitiator);
          console.log('Current user:', documentSnapshot.id);
          console.log('Current user ID:', auth().currentUser!.uid);
          console.log('Chat users:', data.userIds);
          console.log('Is current user initiator:', isCurrentUserInitiator);
        }
      });

    return () => {
      chatSubscribe();
    };
  }, [key]);

  const unreadMessagesCount = useMemo(() => {
    return currentChat?.history.filter(
      (message: any) => message?.readUserIds && !message.readUserIds.includes(currentChat.userIds[1]),
    ).length;
  }, [currentChat?.history, currentChat?.userIds]);

  const onSendMessage = async (text: string) => {
    if (currentChat && currentChat?.status != 'open' && currentChat?.type !== 'contact-pyxi') {
      const chatRef = firestore().collection('chats').doc(key);
      await FirebaseChatsService.createOrganisationChat({
        user_id1: moment().unix().toString(),
        user_id2: moment().unix().toString(),
        user_name1: moment().unix().toString(),
        user_name2: moment().unix().toString(),
        user_image: moment().unix().toString(),
        isTechnical: false,
        event: currentChat.event,
      });
      const chatsSnapshot = await firestore().collection('chats').get();
      const totalChats = chatsSnapshot.size;
      const issueNumber = [totalChats];
      let updatedIssueNumber = [totalChats];
      if (currentChat?.issueNumber) {
        updatedIssueNumber = [...issueNumber, ...currentChat?.issueNumber];
      }
      const currentChatUpdate = {...currentChat};
      currentChatUpdate.issueNumber = updatedIssueNumber;
      setCurrentChat(currentChatUpdate as ChatTypeWithKey);
      await chatRef.update({status: 'open', issueNumber: updatedIssueNumber});
    }
    flatListRef?.current?.scrollToOffset({animated: true, offset: 0});
    await FirebaseChatsService.pushMessageForUsers({
      chat_id: key,
      user_id: auth().currentUser!.uid,
      user_image: currentUser?.photo || '',
      user_name: `${currentUser?.first_name || ''} ${currentUser?.last_name || ''}`,
      text: text,
    });
  };

  const handleAccept = async () => {
    setIsAccepted(true);
    await acceptChat(key); // Зберегти стан прийняття чату
  };

  const handleReject = async () => {
    await firestore().collection('chats').doc(key).delete();
    navigation.goBack();
  };

  const onEventIssueClick = () => {
    onSendMessage(
      `Thanks for confirming it's an event related issue, ${currentChat?.users[0]}! Can you please provide more details about the issue?`,
    );
  };

  const onCloseIssue = async () => {
    const chatRef = firestore().collection('chats').doc(key);
    await chatRef.update({status: 'closed'});
    navigation.goBack();
    Notifier.showNotification({
      title: 'Your support issue has been successfully closed. If you need further assistance, feel free to reach out!',
      Component: NotifierComponents.Alert,
      componentProps: {
        alertType: 'success',
      },
    });
  };

  const onTechnicalIssueClick = async () => {
    const chatId = await FirebaseChatsService.createOrganisationChat({
      user_id1: auth().currentUser!.uid,
      user_id2: host?.uid + '',
      user_name1: `${currentUser?.first_name} ${currentUser?.last_name || ''}`,
      user_name2: (host as Business)?.name
        ? (host as Business)?.name
        : (host as unknown as User)?.last_name
          ? `${(host as unknown as User)?.first_name} ${(host as unknown as User)?.last_name || ''}`
          : (host as unknown as User)?.first_name || '',
      user_image: currentUser?.photo + '',
      isTechnical: true,
      event: null,
    });

    const chatRef = firestore().collection('chats').doc(chatId);
    const doc = await chatRef.get();
    if (doc.exists) {
      const chatData = doc.data() as ChatType;
      const updatedMessages = chatData.history.map((message: any) => {
        if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
          console.log('Updating message:', message);
          return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
        }
        return message;
      });

      await chatRef.update({history: updatedMessages});
    }

    navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
  };

  if (isConfirmationRequired) {
    return (
      <View style={{flex: 1}}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingBottom: 10,
            marginTop: Platform.OS === 'android' ? 30 : 50,
          }}
          onPress={defaultCallback}>
          <ChevronIcon />
          <Text style={{color: '#FF9500', fontWeight: '600'}}>{t('generic.back')}</Text>
        </TouchableOpacity>

        <KeyboardGestureArea interpolator="ios" enableSwipeToDismiss style={{flex: 1}}>
          <Animated.View style={[scrollViewStyle, {paddingHorizontal: 5}]}></Animated.View>
        </KeyboardGestureArea>

        <View
          style={{
            padding: 20,
            borderTopWidth: 1,
            borderTopColor: '#ccc',
            backgroundColor: '#f9f9f9',
          }}>
          <Text style={{fontSize: 14, marginBottom: 20, textAlign: 'center'}}>{currentChat?.history[0].message}</Text>

          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <TouchableOpacity
              style={{
                padding: 12,
                borderRadius: 18,
                marginRight: 10,
                flex: 1,
                alignItems: 'center',
                backgroundColor: '#5f50ad',
              }}
              onPress={onTechnicalIssueClick}>
              <Text style={{color: '#fff', fontWeight: '600'}}>{t('technicalissue')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: 12,
                backgroundColor: '#5f50ad',
                borderRadius: 18,
                marginLeft: 10,
                flex: 1,
                alignItems: 'center',
              }}
              onPress={onEventIssueClick}>
              <Text style={{color: '#fff', fontWeight: '600'}}>{t('eventissue')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  if (isAccepted === null) {
    return null;
  }

  const selectedEvent = currentChat?.event;

  const onEventClick = () => {
    if (selectedEvent) {
      navigation.navigate(SCREENS.HOME_STACK, {
        eventId: selectedEvent.event_id,
      });
    }
  };

  const onHeaderUserClick = () => {
    if (otherUser?.uid) {
      navigation.navigate(SCREENS.PERSONAL_INFO, {
        user: {
          tag: otherUser?.uid + 'image',
          source: otherUser?.photo || '',
          description: otherUser?.description || '',
          name: `${otherUser?.first_name} ${otherUser?.last_name || ''}`,
          user_id: otherUser?.uid || '',
          eventName: '',
        },
      });
    } else if (otherBusinessUser?.uid) {
      navigation.navigate(SCREENS.PERSONAL_INFO, {
        user: {
          tag: otherBusinessUser?.uid + 'image',
          source: otherBusinessUser?.photo || '',
          description: otherBusinessUser?.description || '',
          name: `${otherBusinessUser?.name}`,
          user_id: otherBusinessUser?.uid || '',
          eventName: '',
        },
      });
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FCF9ED'}}>
      <StatusBar barStyle={'dark-content'} />
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: Platform.OS === 'android' ? 30 : 55,
          paddingHorizontal: 20,
          paddingBottom: 10,
          borderBottomWidth: 1,
          borderColor: 'gray',
          alignItems: 'center',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <TouchableOpacity onPress={defaultCallback}>
            <ChevronIcon />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onHeaderUserClick}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 10,
            }}>
            {image ? (
              <Image
                source={{uri: image}} // assuming `userImage` is a valid image URL string
                resizeMode={'cover'}
                style={{
                  width: 26,
                  height: 26,
                  borderRadius: 20,
                  marginRight: 10,
                }}
              />
            ) : (
              <View
                style={[
                  {
                    width: 26,
                    height: 26,
                    borderRadius: 20,
                    marginRight: 10,
                  },
                  {backgroundColor: 'black', alignItems: 'center', justifyContent: 'center'},
                ]}>
                <LogoIcon color="white" />
              </View>
            )}
            <Text style={{fontSize: 14, fontWeight: 'bold', color: '#000'}}>
              {userName}
              {currentChat?.issueNumber && (
                <Text>
                  {' - '}#{currentChat?.issueNumber[0]}
                </Text>
              )}
            </Text>
          </TouchableOpacity>
        </View>
        {currentChat?.status == 'open' && false && (
          <TouchableOpacity onPress={() => onCloseIssue()}>
            <Text
              style={{
                fontSize: 14,
                fontWeight: 'bold',
                color: 'blue',
              }}>
              Close Issue
            </Text>
          </TouchableOpacity>
        )}
      </View>
      {false && selectedEvent && (
        <TouchableOpacity onPress={onEventClick} style={styles.eventCard}>
          <View style={styles.iconContainer}>
            <LikeButton liked={selectedEvent?.user_liked || false} eventId={selectedEvent?.event_id} />
          </View>
          <ImageBackground style={styles.imageEvent} resizeMode={'cover'} source={{uri: selectedEvent?.image_url}}>
            <View style={[StyleSheet.absoluteFill, {backgroundColor: 'rgba(0, 0, 0, 0.4)'}]} />
            <Text numberOfLines={1} style={styles.eventTitle}>
              {selectedEvent?.name}
            </Text>
            <View style={styles.dateRangeContainer}>
              <Text
                style={{
                  color: '#ffff',
                  fontSize: 13,
                  fontWeight: 'bold',
                }}>
                {`${moment.utc(selectedEvent?.start_date).format('DD MMM YYYY')} - ${moment.utc(selectedEvent?.end_date).format('DD MMM YYYY')}`}
              </Text>
            </View>
          </ImageBackground>
        </TouchableOpacity>
      )}
      <KeyboardGestureArea interpolator="ios" enableSwipeToDismiss style={{flex: 1}}>
        <Animated.View style={[scrollViewStyle, {paddingHorizontal: 5}]}>
          <AnimatedFlatList
            inverted
            ref={flatListRef}
            data={currentChat?.history}
            renderItem={({item, index}) => <ChatItem item={item} chatType={currentChat!.type} />}
            keyExtractor={(item, index) => 'key' + index}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={100}
          />
        </Animated.View>
      </KeyboardGestureArea>

      {currentChat?.status != 'open' && currentChat?.issueNumber && (
        <Text style={{alignSelf: 'center', marginTop: 10, marginBottom: 10, color: 'gray'}}>
          Chat #{currentChat?.issueNumber.length > 0 ? currentChat?.issueNumber[0] : ''} is closed, to reopen it send a
          new message.
        </Text>
      )}

      {isAccepted === false && !isInitiator && currentChat?.type !== 'contact-pyxi' && (
        <View
          style={{
            padding: 20, // Збільшено padding для збільшення висоти блоку
            borderTopWidth: 1,
            borderTopColor: '#ccc',
            backgroundColor: '#f9f9f9',
          }}>
          {currentChat?.eventName ? (
            <Text style={{fontSize: 14, marginBottom: 20, textAlign: 'center'}}>
              {currentChat?.users[1]} wants to chat with you about {currentChat?.eventName}.
            </Text>
          ) : (
            <Text style={{fontSize: 14, marginBottom: 20, textAlign: 'center'}}>
              {currentChat?.users[1]} wants to chat with you about the {currentChat?.eventName} event.
            </Text>
          )}
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <TouchableOpacity
              style={{
                padding: 12, // Збільшено padding для кнопок
                backgroundColor: '#fff',
                borderRadius: 18,
                marginRight: 10,
                flex: 1,
                alignItems: 'center',
                borderColor: '#6c757d',
                borderWidth: 1,
              }}
              onPress={handleReject}>
              <Text style={{color: '#6c757d', fontWeight: '600'}}>{t('decline')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: 12, // Збільшено padding для кнопок
                backgroundColor: '#5f50ad',
                borderRadius: 18,
                marginLeft: 10,
                flex: 1,
                alignItems: 'center',
              }}
              onPress={handleAccept}>
              <Text style={{color: '#fff', fontWeight: '600'}}>{t('accept')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {(isAccepted || isInitiator || currentChat?.type == 'contact-pyxi') && (
        <ChatInput submit={text => onSendMessage(text)} h={height} progress={progress} />
      )}
    </View>
  );
};

export default ChatWithUsers;

const styles = StyleSheet.create({
  iconContainer: {
    zIndex: 100,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 35,
    height: 35,
    borderRadius: 35,
    position: 'absolute',
    top: 5,
    right: 10,
    flexDirection: 'row',
    gap: 10, // Відступ між іконками
  },
  imageEvent: {
    width: '100%',
    height: '100%',
  },
  dateRangeContainer: {
    zIndex: 1,
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 10,
    padding: 6,
  },
  eventTitle: {
    fontWeight: 'bold',
    color: 'white',
    fontSize: 18,
    position: 'absolute',
    bottom: 10,
    left: 10,
  },
  eventCard: {
    height: 80,
    width: '90%',
    marginTop: 10,
    alignSelf: 'center',
    zIndex: 2000,
    borderRadius: 10,
    overflow: 'hidden',
  },
});
