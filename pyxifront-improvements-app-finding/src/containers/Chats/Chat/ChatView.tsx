/* eslint-disable react-native/no-inline-styles */
import {BlurView} from '@react-native-community/blur';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useNavigation} from '@react-navigation/native';
import {FlashList} from '@shopify/flash-list';
import moment from 'moment';
import React, {useEffect, useLayoutEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, StatusBar, Text, TouchableOpacity, View} from 'react-native';
import Animated, {FadeInLeft, FadeInRight, FadeOutLeft, FadeOutRight} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getTimestamp} from '~Utils/Time';
import {getLastMessage} from '~Utils/chat';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {ChevronIcon} from '~assets/icons';
import Button from '~components/Button';
import {ChatRow} from '~components/Chat';
import ConciergeChatRow from '~components/Chat/ChatRow/ConciergeChatRow';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import {
  ChatType,
  ChatTypeWithKey,
  ConciergeChatMessageType,
  ConciergeChatType,
  ConciergeChatTypeWithKey,
} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';

export default function Chat() {
  const uid = auth().currentUser?.uid;
  const [chats, setChats] = useState<ChatTypeWithKey[]>([]);
  // const [loading, setLoading] = useState(true);
  const [selectedChats, setSelectedChats] = useState(0);
  const [sortedByTypeChats, setSortedByTypeChats] = useState<(ConciergeChatTypeWithKey | ChatTypeWithKey)[]>([]);
  const [chatWithConcierge, setChatWithConcierge] = useState<ConciergeChatTypeWithKey>();
  const [broadcastMessages, setBroadcastMessages] = useState([]);
  const [oneToOneMessages, setOneToOneMessages] = useState<ConciergeChatMessageType[]>([]);
  const [contactChat, setContactChat] = useState<ChatTypeWithKey | undefined>();
  // const [adminChats, setAdminChats] = useState<ConciergeChatTypeWithKey[]>([]);
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {t} = useTranslation();
  const defaultCallback = () => {
    navigation.goBack();
  };
  const {data: userAccount} = useGetUserAccount(auth().currentUser?.uid);
  const {data: hostBusiness} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');

  const chatItems = [
    {
      id: 0,
      text: t('chat.my_conversations'),
    },
    {
      id: 1,
      text: t('chat.groups'),
    },
  ];

  useLayoutEffect(() => {
    navigation.addListener('focus', () => {
      setIsTabBarDisabled(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2' && hostBusiness?.uid && userAccount?.uid) {
      FirebaseChatsService.createContactUsChat({
        user_id1: auth().currentUser!.uid + '',
        user_id2: hostBusiness?.uid + '',
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: hostBusiness?.name || 'Pyxi',
      });
    }
  }, [uid, hostBusiness, userAccount]);

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
      const unsubscribe = firestore()
        .collection('chats')
        .where('userIds', 'array-contains', uid)
        .onSnapshot(querySnapshot => {
          const conversations: ChatTypeWithKey[] = [];
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data() as ChatType; // Cast Firestore data to our ChatData type
            conversations.push({
              ...data,
              key: documentSnapshot.id,
            });
          });
          // Sort chats by timestamp of the last message
          const sortedConversations =
            conversations?.sort((a, b) => {
              const timestampA = getTimestamp(a.history);
              const timestampB = getTimestamp(b.history);

              return timestampB - timestampA; // For descending order
            }) || [];

          const supportChat = sortedConversations.filter(chat => chat.type === 'contact-pyxi');
          if (supportChat && supportChat.length > 0) {
            console.log(supportChat, 'supportChat');

            setContactChat(supportChat[0]);
          }
          setChats([...sortedConversations]);
        });

      // Clean up subscription on unmount
      return () => unsubscribe();
    }
  }, [uid, selectedChats]);

  useEffect(() => {
    logScreenView('Chat', 'ChatView');
  }, []);

  useEffect(() => {
    const oneOnOneUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('userId', '==', uid)
      .where('type', '==', 'one-on-one')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data() as ConciergeChatType;
          if (data) {
            const sortedMessages = [...broadcastMessages, ...data.messages].sort((a, b) => {
              return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
            });
            setChatWithConcierge({
              ...data,
              key: documentSnapshot.id,
              messages: [...sortedMessages],
            });
            setOneToOneMessages(data.messages);
          }
        });
      });

    const broadcastUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('type', '==', 'broadcast')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data();
          setBroadcastMessages(data.messages);
        });
      });

    return () => {
      oneOnOneUnsubscribe();
      broadcastUnsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const sortedMessages = [...oneToOneMessages, ...broadcastMessages].sort((a, b) => {
      return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
    });
    setChatWithConcierge(prev => {
      if (prev) {
        return {...prev, messages: [...sortedMessages]};
      }
    });
  }, [oneToOneMessages, broadcastMessages]);

  useEffect(() => {
    if (!chatWithConcierge) {
      return;
    }
    const sortedChats =
      selectedChats === 0
        ? [chatWithConcierge, ...chats.filter(chat => chat.type !== 'group')]
        : [...chats.filter(chat => chat.type === 'group')];

    setSortedByTypeChats(sortedChats);
  }, [selectedChats, chats, chatWithConcierge, uid]);

  const chartsList = useMemo(() => {
    return (
      <FlashList
        data={sortedByTypeChats}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingTop: 10,
          paddingBottom: 100,
        }}
        /* ListHeaderComponent={() => {
          return (
            <>
              {contactChat && (
                <Animated.View
                  entering={selectedChats ? FadeInRight.delay(200).duration(300) : FadeInLeft.delay(200).duration(300)}
                  exiting={
                    !selectedChats ? FadeOutLeft.delay(200).duration(250) : FadeOutRight.delay(200).duration(250)
                  }>
                  <ChatRow chat={contactChat} />
                </Animated.View>
              )}
            </>
          );
        }} */
        estimatedItemSize={100}
        renderItem={({item, index}) => {
          switch (item?.type) {
            case 'group':
            case 'private':
              return (
                <Animated.View
                  entering={selectedChats ? FadeInRight.delay(200).duration(300) : FadeInLeft.delay(200).duration(300)}
                  exiting={
                    !selectedChats ? FadeOutLeft.delay(200).duration(250) : FadeOutRight.delay(200).duration(250)
                  }>
                  <ChatRow chat={item} />
                </Animated.View>
              );
            case 'contact-pyxi':
              return (
                <Animated.View
                  entering={selectedChats ? FadeInRight.delay(200).duration(300) : FadeInLeft.delay(200).duration(300)}
                  exiting={
                    !selectedChats ? FadeOutLeft.delay(200).duration(250) : FadeOutRight.delay(200).duration(250)
                  }>
                  <ChatRow chat={item} />
                </Animated.View>
              );
            case 'organisation':
              return (
                <Animated.View
                  entering={selectedChats ? FadeInRight.delay(200).duration(300) : FadeInLeft.delay(200).duration(300)}
                  exiting={
                    !selectedChats ? FadeOutLeft.delay(200).duration(250) : FadeOutRight.delay(200).duration(250)
                  }>
                  <ChatRow chat={item} />
                </Animated.View>
              );
            case 'broadcast':
            case 'one-on-one':
            /* return (
                <Animated.View
                  entering={
                    selectedChats
                      ? FadeInRight.delay(200 + 10 * index).duration(300)
                      : FadeInLeft.delay(200 + 10 * index).duration(300)
                  }
                  exiting={
                    !selectedChats
                      ? FadeOutLeft.delay(200 + 10 * index).duration(250)
                      : FadeOutRight.delay(200 + 10 * index).duration(250)
                  }>
                  <ConciergeChatRow chat={item} />
                </Animated.View>
              ); */
            default:
              return null;
          }
        }}
        keyExtractor={item => {
          return 'key' + item.key;
        }}
        showsVerticalScrollIndicator={false}
      />
    );
  }, [selectedChats, sortedByTypeChats, top]);

  const handleChatWithPyxi = () => {
    if (contactChat) {
      const interlocutorId = contactChat.userIds.find(userId => userId !== uid);
      const interlocutorMessage = contactChat.history.find(message => message.sender_id === interlocutorId);
      const interlocutorImage = interlocutorMessage?.sender_image;

      const image = contactChat.eventImage || interlocutorImage;
      const userIndex = contactChat.userIds.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase());
      const userName = contactChat.users[userIndex];

      navigation.navigate(SCREENS.USER_CHAT, {chatId: contactChat.key, image: image, userName: userName});
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FCF9ED'}}>
      <StatusBar barStyle={'dark-content'}/>
      {/* {Platform.OS === 'ios' ? (
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            flexDirection: 'row',
            backgroundColor: 'rgba(199,199,204,0.4)',
            zIndex: 4,
          }}>
          {(uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? [chatItems[0]] : chatItems).map((item, index) => (
            <View
              key={item.id}
              style={{
                width: `${uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? 100 : 50}%`,
                height: 45 + (top || 10),
                paddingTop: top || 10,
                borderBottomWidth: 1,
                borderRightWidth: !index ? 1 : 0,
                borderColor: 'rgb(199,199,204)',
              }}>
              <TouchableOpacity
                onPress={() => setSelectedChats(item.id)}
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{fontWeight: '500', fontSize: 15, color: selectedChats === item.id ? 'black' : '#8E8E93'}}>
                  {uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? 'Admin Panel' : item.text}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      ) : (
        <View
          style={{
            flexDirection: 'row',
            zIndex: 4,
          }}>
          {(uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? [chatItems[0]] : chatItems).map((item, index) => (
            <View
              key={item.id}
              style={{
                width: `${uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? 100 : 50}%`,
                height: 45 + (top || 10),
                paddingTop: top || 10,
                borderBottomWidth: 1,
                borderRightWidth: !index ? 1 : 0,
                borderColor: 'rgb(199,199,204)',
              }}>
              <TouchableOpacity
                onPress={() => setSelectedChats(item.id)}
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{fontWeight: '500', fontSize: 15, color: selectedChats === item.id ? 'black' : '#8E8E93'}}>
                  {uid === 'wLJLEn8J6oN9RpPyep2BjdnagcA2' ? 'Admin Panel' : item.text}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )} */}

      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingBottom: 10,
            marginTop: Platform.OS === 'android' ? 30 : 50,
          }}
          onPress={defaultCallback}>
          <ChevronIcon />
          <Text style={{color: '#FF9500', fontWeight: '600'}}>{t('generic.back')}</Text>
        </TouchableOpacity>
        {contactChat && (
          <Button
            containerStyle={{
              padding: 4,
              borderRadius: 50,
              backgroundColor: '#F5A865',
              marginRight: 20,
              marginTop: Platform.OS === 'android' ? 30 : 50,
            }}
            textStyle={{
              color: 'white',
            }}
            onPress={handleChatWithPyxi}
            label={'Chat With Pyxi'}
          />
        )}
      </View>

      {chartsList}

      {/* {Platform.OS === 'ios' && (
        <BlurView
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 45 + (top || 10),
            flexDirection: 'row',
            backgroundColor: 'rgba(199,199,204,0.4)',
            zIndex: 3,
          }}
          blurType="light"
          blurAmount={2}
        />
      )} */}
    </View>
  );
}
