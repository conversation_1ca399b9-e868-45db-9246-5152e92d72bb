import {useNavigation, useRoute} from '@react-navigation/native';
import {useEffect, useLayoutEffect, useState} from 'react';
import {Platform, StatusBar, View} from 'react-native';
import HomeScreenTabs from '~components/HomeScreenComponent';
import MyEventTabs from '~components/HomeScreenComponent/MyEventTabs';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import { logScreenView } from '~Utils/firebaseAnalytics';

const HomeScreen = () => {
  const {setIsTabBarDisabled} = useTabBar();
  const {reset} = useOnboardingStore();
  const navigation = useNavigation();
  const routes = useRoute();
  const [isMyEvent, setMyEvent] = useState();

  useEffect(() => {
    if (routes.params && routes.params.myEvent) {
      setMyEvent(routes.params.myEvent);
    }
  }, [routes.params]);

  useLayoutEffect(() => {
    reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    logScreenView('Home', 'Home');
  }, []);

  return (
    <>
      <View style={{flex: 1, backgroundColor: '#fff'}}>{isMyEvent ? <MyEventTabs /> : <HomeScreenTabs />}</View>
    </>
  );
};

export default HomeScreen;
