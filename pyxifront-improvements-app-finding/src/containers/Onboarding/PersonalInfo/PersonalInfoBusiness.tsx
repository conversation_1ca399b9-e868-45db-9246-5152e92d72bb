import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import { logScreenView } from '~Utils/firebaseAnalytics';
import {reverseGeocode} from '~Utils/location';
import Button from '~components/Button';
import CustomBox from '~components/CustomBox';
import {CustomTextInput} from '~components/CustomTextInput';
import {GoBackHeader} from '~components/GoBackHeader';
import LocationModal from '~components/LocationModal';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';

const PersonalInfoBusiness = () => {
  const {t} = useTranslation();
  const {data} = useGetBusinessAccount(auth().currentUser?.uid || '');
  const {mutateAsync: handleUpdateBusiness} = useUpdateBusiness();

  const validationSchema = yup.object().shape({
    name: yup.string().required('onboarding.first_name_error'),
    description: yup.string().min(40, 'onboarding.min_length_error').required('onboarding.description_error'),
  });

  const navigation = useNavigation();
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const {bottom} = useSafeAreaInsets();
  const [addressName, setAddressName] = useState('');
  const [coords, setCoords] = useState({latitude: data?.coords?.lat, longitude: data?.coords?.long});

  const [locationError, setLocationError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    const func = async () => {
      if (data?.coords) {
        setCoords({latitude: data?.coords?.lat, longitude: data?.coords?.long});
        const address = await reverseGeocode({
          coords: {latitude: data?.coords?.lat, longitude: data?.coords?.long},
        });
        setAddressName(address || '');
      }
    };
    func();
  }, [data]);

  useEffect(() => {
    logScreenView('Personal Info Business', 'PesonalInfoBusiness');
  }, []);

  return (
    <>
      <GoBackHeader />
      <View style={{flex: 1}}>
        <Formik
          onSubmit={async values => {
            setIsLoading(true);
            try {
              if (coords.latitude && coords.longitude) {
                await handleUpdateBusiness({
                  uid: auth().currentUser!.uid,
                  name: values.name,
                  description: values.description,
                  coords: {lat: coords.latitude, long: coords.longitude},
                  business_type_id: data?.business_type_id || 1,
                  photo: data?.photo || '',
                  is_registration_finished: !!data?.is_registration_finished,
                  email: data?.email || '',
                });
                navigation.goBack();
              }
            } catch (e) {
              setIsLoading(false);
            }
          }}
          initialValues={{
            name: data?.name || '',
            description: data?.description || '',
            location: addressName || '',
          }}
          validationSchema={validationSchema}>
          {({values, handleChange, handleSubmit, errors}) => {
            return (
              <>
                <ScrollView
                  style={{flex: 1}}
                  contentContainerStyle={{paddingHorizontal: 16, paddingBottom: 100, marginTop: 80}}>
                  <Text
                    style={{
                      lineHeight: 41,
                      fontSize: 34,
                      color: '#1D1E20',
                      fontWeight: '700',
                      marginTop: 12,
                      marginBottom: 16,
                    }}>
                    {t('settings.personalInfo')}
                  </Text>
                  <CustomTextInput
                    value={values.name}
                    onChangeValue={handleChange('name')}
                    description={t('settings.name')}
                    errorText={errors.name?.toString()}
                    placeholder={t('settings.name_placeholder')}
                  />
                  <CustomTextInput
                    value={values.description}
                    onChangeValue={handleChange('description')}
                    description={t('settings.about_me')}
                    errorText={errors.description?.toString()}
                    placeholder="Please enter about your self"
                    isMultiline
                  />

                  <CustomBox
                    description={t('settings.location')}
                    handlePress={() => {
                      setIsLocationModalOpen(true);
                      setLocationError('');
                    }}
                    value={values.location || addressName}
                    errorText={locationError}
                  />
                </ScrollView>

                <Button
                  label={t('settings.save_changes')}
                  isLoading={isLoading}
                  onPress={() => handleSubmit()}
                  containerStyle={{
                    backgroundColor: '#FF9500',
                    marginHorizontal: 16,
                    marginBottom: Platform.OS === 'ios' ? bottom : 16,
                  }}
                  textStyle={{color: '#fff'}}
                />

                <LocationModal
                  isVisible={isLocationModalOpen}
                  close={() => {
                    setIsLocationModalOpen(false);
                  }}
                  onLocationChange={object => {
                    if (object?.latitude && object.longitude) {
                      setCoords({latitude: object?.latitude, longitude: object?.longitude});
                      handleChange('location')(object?.address || '');
                    }
                  }}
                />
              </>
            );
          }}
        </Formik>
      </View>
    </>
  );
};

export default PersonalInfoBusiness;
