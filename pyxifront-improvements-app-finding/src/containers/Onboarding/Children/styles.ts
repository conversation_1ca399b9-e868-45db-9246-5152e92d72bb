import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
    container: {flex: 1, alignItems: 'center', paddingHorizontal: 30},
    titleText: {
        textAlign: 'center',
        marginTop: 40,
        fontSize: 18,
        fontWeight: '600',
        color: '#1D1E20',
        letterSpacing: 0.2,
        marginBottom: 20,
      },
    boxesWrapper:{
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        flexWrap: 'wrap',
      },
    bottomButtonWrapper: {
        width: '100%',
        flex: 1,
        justifyContent: 'flex-end',
        // marginBottom: bottom + 20,
      },
    bottomButton: {
        width: '100%',
        height: 40,
        borderRadius: 6,
        // backgroundColor:
        //   !!children.length && !!children.filter(age => age === undefined).length ? '#F1F1F3' : '#FF9500',
        alignItems: 'center',
        justifyContent: 'center',
    },
    bottomButtonText: {              
        fontSize: 15,
        fontWeight: '500',
    },
    
      
    
});

export default styles;