import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {DropdownArrowIcon} from '~assets/icons';
import Button from '~components/Button';
import CustomBox from '~components/CustomBox';
import {GoBackHeader} from '~components/GoBackHeader';
import ModalWithAges from '~components/ModalWithItems/ModalWithAges';
import ModalWithQuantity from '~components/ModalWithItems/ModalWithQuantity';
import {SCREENS} from '~constants';
import {useGetUserChildren} from '~hooks/user/useGetUserChildren';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {NavigationProps} from '~types/navigation/navigation.type';

const ChildrenQuantity = ({onSubmit, isFromSettings = false}: {onSubmit?: () => void; isFromSettings?: boolean}) => {
  const {t} = useTranslation();
  const [quantityModalIsVisible, setQuantityModalIsVisible] = useState(false);
  const [children, setChildren] = useState<string[]>([]);
  const [ageModalIsVisible, setAgeModalIsVisible] = useState(false);
  const [childrenQuantity, setChildrenQuantity] = useState(0);
  const [chosenAgeIndex, setChosenAgeIndex] = useState(0);
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setPersonalChildren} = useOnboardingStore();
  const {data: personalChildren} = useGetUserChildren();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const changeValueAtIndex = (index: number, newValue: string) => {
    setChildren(prevArr => {
      const newArr = [...prevArr];
      newArr[index] = newValue;
      return newArr;
    });
  };

  useEffect(() => {
    if (isFromSettings && personalChildren) {
      setChildren(personalChildren.map(child => child.child_age.toString()));
      setChildrenQuantity(personalChildren.length);
    }
  }, [personalChildren, isFromSettings]);
  useEffect(() => {
    childrenQuantity ? setChildren([...new Array(childrenQuantity)]) : setChildren([]);
  }, [childrenQuantity, setChildren]);

  const childrenQuantityBoxes = useMemo(
    () =>
      children?.map((value, index) => (
        <CustomBox
          handlePress={() => {
            setAgeModalIsVisible(true);
            setChosenAgeIndex(index);
          }}
          key={`${childrenQuantity}-${index}`}
          icon={DropdownArrowIcon()}
          defaultValue={t('onboarding.age')}
          value={value}
          buttonWidth={children.length % 3 === 0 ? '30%' : '48%'}
          withoutError
        />
      )),
    [children, childrenQuantity],
  );

  return (
    <>
      {!onSubmit && <GoBackHeader />}
      <View style={{flex: 1, alignItems: 'center', paddingHorizontal: 30, paddingTop: 100}}>
        <Text
          style={{
            textAlign: 'center',
            fontSize: 18,
            fontWeight: '600',
            color: '#1D1E20',
            letterSpacing: 0.2,
            marginBottom: 20,
          }}>
          {t('onboarding.how_many_kids_desc')}
        </Text>
        <CustomBox
          handlePress={() => setQuantityModalIsVisible(true)}
          icon={DropdownArrowIcon()}
          description={t('onboarding.how_many_kids')}
          defaultValue={t('onboarding.select_or_pass')}
          value={childrenQuantity}
          withoutError
        />
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '100%',
            flexWrap: 'wrap',
          }}>
          {childrenQuantityBoxes}
        </View>

        <Text
          style={{
            textAlign: 'center',
            fontSize: 15,
            fontWeight: '400',
            color: '#1D1E20',
            letterSpacing: 0.2,
            marginTop: 20,
          }}>
          {t('onboarding.we_are_asking_for_the_kids_age')}
        </Text>
        
        <View
          style={{
            width: '100%',
            flex: 1,
            justifyContent: 'flex-end',
            marginBottom: bottom + 20,
          }}>
          <Button
            label={t('generic.continue')}
            disabled={!!children.length && !!children.filter(age => age === undefined).length}
            isLoading={isLoading}
            containerStyle={{
              width: '100%',
              height: 40,
              borderRadius: 6,
              backgroundColor:
                !!children.length && !!children.filter(age => age === undefined).length ? '#F1F1F3' : '#FF9500',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={
              onSubmit
                ? onSubmit
                : () => {
                    setIsLoading(true);
                    setPersonalChildren(children);
                    navigation.navigate(SCREENS.ONBOARDING_SUBCATEGORIES);
                  }
            }
            textStyle={{
              fontSize: 15,
              fontWeight: '500',
              color: !!children.length && !!children.filter(age => age === undefined).length ? '#8E8E93' : 'white',
            }}
          />
        </View>
        <ModalWithQuantity
          isVisible={quantityModalIsVisible}
          close={() => setQuantityModalIsVisible(false)}
          onPress={(value: number) => () => {
            setChildrenQuantity(value);
          }}
          chosenQuantity={childrenQuantity}
        />
        <ModalWithAges
          isVisible={ageModalIsVisible}
          close={() => setAgeModalIsVisible(false)}
          onPress={value => () => {
            changeValueAtIndex(chosenAgeIndex, value);
          }}
          chosenAge={children[chosenAgeIndex] || ''}
        />
      </View>
    </>
  );
};

export default ChildrenQuantity;
