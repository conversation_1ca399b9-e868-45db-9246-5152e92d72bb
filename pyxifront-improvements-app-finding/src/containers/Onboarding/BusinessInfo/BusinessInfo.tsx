import auth from '@react-native-firebase/auth';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView, View } from 'react-native';
import { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context';
import * as yup from 'yup';
import { DropdownArrowIcon } from '~/assets/icons';
import CustomBox from '~/components/CustomBox';
import { CustomTextInput } from '~/components/CustomTextInput';
import LocationModal from '~/components/LocationModal';
import useGetCurrentPosition from '~/components/LocationModal/hooks/useGetCurrentPosition';
import Button from '~components/Button';
import { GoBackHeader } from '~components/GoBackHeader';
import ProgressDots from '~components/ProgressDots';
import { ProfileImageComponent } from '~components/Settings';
import { SCREENS } from '~constants';
import { useMapsContext } from '~providers/maps/zustand';
import { useOnboardingStore } from '~providers/onboarding/zustand';
import { NavigationProps } from '~types/navigation/navigation.type';
import { logScreenView } from '~Utils/firebaseAnalytics';

const validationSchema = yup.object().shape({
  name: yup
    .string()
    .matches(/^[a-zA-Z]+(?: [a-zA-Z]+)?$/, 'onboarding.first_name_error')
    .required('onboarding.first_name_error'),
  description: yup.string().min(40, 'onboarding.min_length_error').required('onboarding.description_error'),
  photoPath: yup.string().required('onboarding.image_required'),
});
export default function BusinessInfo() {
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();

  const [locationModalIsVisible, setLocationModalIsVisible] = useState(false);
  const {currentPositionState} = useMapsContext();
  const [locationError, setLocationError] = useState('');
  const {width} = useSafeAreaFrame();
  const {bottom} = useSafeAreaInsets();
  const {businessOnboarding, setBusinessOnboardingProfileInfo} = useOnboardingStore();
  const {getCurrentPosition} = useGetCurrentPosition();
  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Business Info', 'BusinessInfo');
  }, []);

  return (
    <>
      <GoBackHeader />
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Formik
          initialValues={{
            name: businessOnboarding.name,
            description: businessOnboarding.description,
            photoPath: businessOnboarding.photo || auth().currentUser?.photoURL,
          }}
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          onSubmit={(values, actions) => {
            setIsLoading(true);
            const infoObj = {
              name: values.name,
              description: values.description,
              coords: {
                lat: currentPositionState!.latitude || 51.5072178,
                long: currentPositionState!.longitude || -0.1275862,
              },
              photo: values.photoPath,
            };

            setBusinessOnboardingProfileInfo(infoObj);
            navigation.navigate(SCREENS.ONBOARDING_SUBCATEGORIES, {isBusiness: true});
          }}
          validationSchema={validationSchema}>
          {formikProps => (
            <View style={{flex: 1, width: '100%'}}>
              <ScrollView bounces={false} style={{flex: 1}} showsVerticalScrollIndicator={false}>
                <ProfileImageComponent
                  value={formikProps.values.photoPath}
                  onChange={(value: string) => formikProps.handleChange('photoPath')(value)}
                  isFromOnboarding
                  errorText={formikProps.touched.photoPath && t(formikProps.errors.photoPath || '')}
                />
                <View style={{paddingHorizontal: 30, marginTop: 20}}>
                  <CustomTextInput
                    description={t('onboarding.company_name')}
                    value={formikProps.values.name}
                    onChangeValue={formikProps.handleChange('name')}
                    errorText={formikProps.touched.name && t(formikProps.errors.name || '')}
                  />
                  <CustomBox
                    description={t('onboarding.business_location')}
                    value={currentPositionState?.address || ''}
                    handlePress={() => {
                      getCurrentPosition();
                      setLocationModalIsVisible(true);
                      setLocationError('');
                    }}
                    icon={DropdownArrowIcon()}
                    errorText={locationError}
                  />
                  <CustomTextInput
                    description={t('onboarding.description_business')}
                    value={formikProps.values.description}
                    onChangeValue={formikProps.handleChange('description')}
                    errorText={formikProps.touched.description && t(formikProps.errors.description || '')}
                    isMultiline
                  />
                </View>
              </ScrollView>
              <View style={{width: width, justifyContent: 'flex-end'}}>
                <View
                  style={{
                    width: '100%',
                    paddingHorizontal: 25,
                    paddingTop: 16,
                    justifyContent: 'flex-end',
                    marginBottom: Platform.OS === 'android' ? 20 : bottom,
                  }}>
                  <Button
                    label={t('generic.continue')}
                    isLoading={isLoading}
                    onPress={(e: any) => {
                      if (!currentPositionState?.address) {
                        setLocationError(t('onboarding.location_error'));
                      }
                      formikProps.handleSubmit(e);
                    }}
                    textStyle={{
                      fontSize: 15,
                      fontWeight: '500',
                      color: 'white',
                    }}
                    containerStyle={{
                      width: '100%',
                      height: 40,
                      borderRadius: 6,
                      backgroundColor: '#FF9500',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 24,
                    }}
                  />
                  <ProgressDots dotsNumber={2} selectedDotNumber={1} />
                </View>
              </View>
              <LocationModal
                isVisible={locationModalIsVisible}
                close={() => {
                  setLocationModalIsVisible(false);
                }}
              />
            </View>
          )}
        </Formik>
      </View>
    </>
  );
}
