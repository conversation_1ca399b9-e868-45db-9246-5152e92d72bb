import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Linking, Platform, Pressable, ScrollView, Text, View, useWindowDimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useHandleLogOut} from '../hooks/useHandleLogOut';
import { logScreenView } from '~Utils/firebaseAnalytics';

const AccountTypeView = () => {
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const {width} = useWindowDimensions();
  const {bottom} = useSafeAreaInsets();
  const [accountType, setAccountType] = useState('personal');
  const {handleLogOut} = useHandleLogOut();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Account Type', 'AccountType');
  }, []);

  const submit = () => {
    setAccountType(accountType);

    if (accountType === 'personal') {
      setIsLoading(true);
      navigation.navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
    } else {
      Linking.openURL("https://partner.pyxi.ai/auth/register")
      // navigation.navigate(SCREENS.ONBOARDING_BUSINESS_INFO);
    }
  };

  return (
    <>
      <GoBackHeader customCallback={handleLogOut} customText={t('settings.sign_out')} />
      <ScrollView
        style={{
          paddingHorizontal: 30,
          width: '100%',
        }}
        bounces={false}
        contentContainerStyle={{flexGrow: 1, paddingTop: 80}}>
        <Text
          style={{
            textAlign: 'center',
            marginTop: Platform.OS === 'ios' ? 40 : 0,
            fontSize: 18,
            fontWeight: '500',
            color: '#1D1E20',
          }}>
          {t('onboarding.choose_account')}
        </Text>
        <Pressable
          onPress={() => {
            setAccountType('personal');
          }}
          style={{
            marginTop: Platform.OS === 'ios' ? 24 : 12,
            paddingHorizontal: 14,
            paddingVertical: 8,
            borderWidth: accountType === 'personal' ? 1.5 : 1,
            borderRadius: 6,
            borderColor: accountType === 'personal' ? '#FF9500' : '#E5E5EA',
          }}>
          <View>
            <FastImage
              source={require('~assets/images/personalImage.jpg')}
              style={{height: (width - 88) * 0.65, width: width - 88}}
            />
          </View>

          <View
            style={{
              marginTop: 4,
              justifyContent: 'flex-start',
            }}>
            <Text style={{color: '#1D1E20', fontWeight: '700', fontSize: 17}}>{t('onboarding.personal')}</Text>
            <Text style={{color: '#1D1E20', fontWeight: '400', fontSize: 12}}>{t('onboarding.personal_desc')}</Text>
          </View>
        </Pressable>
        <Pressable
          onPress={() => {
            Linking.openURL("https://partner.pyxi.ai/auth/register/")
            setAccountType('business');
          }}
          style={{
            marginTop: 8,
            paddingHorizontal: 14,
            paddingVertical: 8,
            borderWidth: accountType === 'business' ? 1.5 : 1,
            borderRadius: 6,
            borderColor: accountType === 'business' ? '#FF9500' : '#E5E5EA',
          }}>
          <View>
            <FastImage
              source={require('~assets/images/businessImage.jpg')}
              style={{height: (width - 88) * 0.65, width: width - 88}}
            />
          </View>

          <View
            style={{
              marginTop: 4,
              justifyContent: 'flex-start',
            }}>
            <Text style={{color: '#1D1E20', fontWeight: '700', fontSize: 17}}>{t('onboarding.business')}</Text>
            <Text style={{color: '#1D1E20', fontWeight: '400', fontSize: 12}}>{t('onboarding.business_desc')}</Text>
          </View>
        </Pressable>
      </ScrollView>
      <View
        style={{
          width: '100%',
          marginTop: 10,
          justifyContent: 'flex-end',
          marginBottom: bottom + 20,
          paddingHorizontal: 25,
        }}>
        <Button
          label={t('generic.continue')}
          isLoading={isLoading}
          onPress={submit}
          textStyle={{
            fontSize: 15,
            fontWeight: '500',
            color: 'white',
          }}
          containerStyle={{
            width: '100%',
            height: 40,
            borderRadius: 6,
            backgroundColor: '#FF9500',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 24,
          }}
        />
      </View>
    </>
  );
};

export default AccountTypeView;
