import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Platform, Text, TextInput, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import { logScreenView } from '~Utils/firebaseAnalytics';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import ProgressDots from '~components/ProgressDots';
import SubcategoriesLists from '~components/SubcategoriesLists';
import {SCREENS} from '~constants';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useCreateUser} from '~hooks/user/useCreateUser';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {useUpdateUserGroups} from '~hooks/user/useUpdateUserGroups';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import {SubCategoryType} from '~types/categories';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';

const CategoryScrollView = () => {
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.ONBOARDING_SUBCATEGORIES>>();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {mutateAsync} = useCreateUser();
  const {
    personalOnboarding,
    categories: selectedCategories,
    subcategories: selectedSubCategories,
  } = useOnboardingStore();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {user} = useUserStore();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {mutateAsync: updateUserGroups} = useUpdateUserGroups();
  const {navigate} = useNavigation<NavigationProps>();
  const isBusiness = params?.isBusiness;

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Categories', 'CategoryScrollView');
  }, []);

  useEffect(() => {
    if (d && data && !searchValue) {
      setCategories(organizeByCategory(selectedCategories, d, data));
    }
  }, [d, data, searchValue]);

  const submit = async () => {
    setIsLoading(true);

    if (isBusiness) {
      navigate(SCREENS.ONBOARDING_GROUP, {isBusiness: true});
    }

    try {
      if (user) {
        setIsLoading(true);
        await updateUserGroups({ids: personalOnboarding.groups});
        await updateUserSubcategories({ids: selectedSubCategories});
        navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        return;
      }

      if (personalOnboarding.photo[0] != 'h') {
        await storage()
          .ref('users/' + auth().currentUser!.uid + '/profile.png')
          .putFile(personalOnboarding.photo);
      }

      const urlPhoto =
        personalOnboarding.photo[0] == 'h'
          ? auth().currentUser?.photoURL
          : await storage()
              .ref('users/' + auth().currentUser!.uid + '/profile.png')
              .getDownloadURL();

      await mutateAsync({
        ...personalOnboarding,
        email: auth().currentUser!.email!,
        photo: urlPhoto,
        uid: auth().currentUser!.uid,
        is_registration_finished: false,
        subcategories: selectedSubCategories,
        coords_real: null,
      });
      navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  useEffect(() => {
    handleSearchCategory();
  }, [searchValue]);

  const handleSearchCategory = () => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      setCategories(organizeByCategory([], d, inputData));
    } else if (data && d) {
      setCategories(organizeByCategory([], d, data));
    }
  };

  const onSubmitPress = () => {
    if (selectedSubCategories.length > 0) {
      submit();
    } else {
      Alert.alert('You can modify your selections later in settings', '', [{text: 'OK', onPress: () => submit()}]);
    }
  };

  const isButtonDisabled = selectedSubCategories.length == 0;

  return (
    <>
      <GoBackHeader />
      <View style={{flex: 1, paddingTop: 50, backgroundColor: 'white', marginTop: 20}}>
        <View style={{width: '100%', alignItems: 'flex-start', paddingHorizontal: 16}}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: 24,
              color: '#1D1E20',
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('signin.tell_us_what_you_like')}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 16,
          }}>
          <TextInput
            placeholder="Search by name"
            value={searchValue}
            onChangeText={setSearchValue}
            style={{
              flex: 1,
              height: 40,
              borderWidth: 1,
              borderColor: '#888',
              borderRadius: 5,
              paddingHorizontal: 10,
              marginRight: 8,
            }}
          />
          <Button
            label="Search"
            onPress={handleSearchCategory}
            containerStyle={{backgroundColor: '#FF9500', height: 40, borderRadius: 5}}
            textStyle={{color: 'white', fontWeight: '600'}}
          />
        </View>
        <SubcategoriesLists searchValue={searchValue}  categoriesList={d} filteredSubcategoriesData={categories} />

        <View
          style={{
            width: '100%',
            paddingHorizontal: 16,
            justifyContent: 'flex-end',
            marginBottom: Platform.OS === 'android' ? 20 : bottom,
          }}>
          <Button
            label={isButtonDisabled ? t('signin.i_will_do_later') : t('generic.submit')}
            isLoading={isLoading}
            onPress={() => onSubmitPress()}
            textStyle={{
              fontSize: 15,
              fontWeight: '500',
              color: 'white',
            }}
            containerStyle={{
              width: '100%',
              height: 40,
              borderRadius: 6,
              backgroundColor: '#FF9500',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 24,
            }}
          />
          <ProgressDots dotsNumber={isBusiness ? 2 : 6} selectedDotNumber={isBusiness ? 2 : 3} />
        </View>
      </View>
    </>
  );
};

export default CategoryScrollView;
