import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ScrollView, TextInput, ActivityIndicator, Platform} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useSubmitOnboardingAnswers} from '~hooks/user/useSubmitOnboardingAnswers';
import auth from '@react-native-firebase/auth';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useUserStore} from '~providers/userStore/zustand';
import { useUpdateUser } from '~hooks/user/useUpdateUser';

type NestedOption = {
  label: string;
  subOptions?: string[];
  allowTextInput?: boolean;
};

type Question = {
  id: number;
  question: string;
  options: (string | NestedOption)[];
  selectedAnswer?: string;
  customText?: string;
};

type Answer = {
  question: string;
  answer: string;
};

const initialQuestions: Question[] = [
  {
    id: 1,
    question: 'Do you consider yourself an introvert, an extrovert or a bit of both?',
    options: ['Introvert', 'Extrovert', 'A bit of both'],
  },
  {
    id: 2,
    question: 'Do you have any strong religious views?',
    options: [
      {
        label: 'Yes',
        subOptions: ['Christian', 'Muslim', 'Hindu', 'Jewish', 'Other'],
        allowTextInput: true,
      },
      'No',
    ],
  },
  {
    id: 3,
    question: 'Do you have any strong political views?',
    options: [
      {
        label: 'Yes',
        subOptions: ['Very left', 'Very right', 'I just get passionate about politics'],
      },
      'No',
    ],
  },
];

const OnboardingQuestion: React.FC = () => {
  const [questionsState, setQuestionsState] = useState<Question[]>(initialQuestions);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setLoading] = useState(false);

  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.EDIT_PREFERANCE>>();
  const {setUser} = useUserStore();
  const navigation = useNavigation<NavigationProps>();

  const userId = auth().currentUser?.uid || '';
  const {mutateAsync: submitOnboardingAnswers} = useSubmitOnboardingAnswers(userId);
  const {data: userData, refetch: refetchUser} = useGetUserAccount(userId);
    const {mutateAsync: updateUser} = useUpdateUser();

  const currentQuestion = questionsState[currentQuestionIndex];

  useEffect(() => {
    if (userData && userData.onboarding_answers && userData.onboarding_answers.length > 0) {
      setUser(userData);
    }
  }, [userData]);

  useEffect(() => {
    if (params?.setting && userData?.onboarding_answers) {
      const preFilledQuestions = initialQuestions.map(q => {
        const found = userData.onboarding_answers.find(a => a.question === q.question);
        if (found) {
          const isNestedQuestion = q.options.some(opt => typeof opt !== 'string');
          if (isNestedQuestion) {
            const nestedOption = q.options.find(opt => typeof opt !== 'string' && opt.subOptions?.includes('Other')) as
              | NestedOption
              | undefined;
            if (nestedOption && nestedOption.allowTextInput) {
              const subOptions = nestedOption.subOptions || [];
              const isCustomAnswer =
                !subOptions.includes(found.answer) && found.answer !== nestedOption.label && found.answer !== 'No';
              if (isCustomAnswer) {
                return {...q, selectedAnswer: 'Other', customText: found.answer};
              }
            }
          }
          return {...q, selectedAnswer: found.answer};
        }
        return q;
      });
      setQuestionsState(preFilledQuestions);
    }
  }, [params?.setting, userData?.onboarding_answers]);

  const handleOptionSelect = (option: string) => {
    setQuestionsState(prev =>
      prev.map((q, idx) => (idx === currentQuestionIndex ? {...q, selectedAnswer: option, customText: undefined} : q)),
    );
  };

  const handleCustomTextChange = (text: string) => {
    setQuestionsState(prev => prev.map((q, idx) => (idx === currentQuestionIndex ? {...q, customText: text} : q)));
  };

  const handleNext = async () => {
    if (currentQuestionIndex < questionsState.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      setLoading(true);
      try {
        const finalAnswers: Answer[] = questionsState.map(q => ({
          question: q.question,
          answer: q.selectedAnswer === 'Other' && q.customText ? q.customText : q.selectedAnswer || '',
        }));

        const response: any = await submitOnboardingAnswers({answers: finalAnswers});

        if (response && response.detail) {
          if (params.setting) {
            navigation.goBack();
          } else {
            await updateUser({
              is_registration_finished: true
            })
            await refetchUser();
          }
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to submit answers:', error);
        setLoading(false);
      }
    }
  };

  const isContinueDisabled = () => {
    const selected = currentQuestion.selectedAnswer;
    if (!selected) return true;
  
    const nestedOption = currentQuestion.options.find(
      opt => typeof opt !== 'string' && (opt as NestedOption).label === selected
    ) as NestedOption | undefined;
  
    if (nestedOption) {
      return true;
    }
  
    return false;
  };
  

  return (
    <View style={styles.mainContainer}>
       <SafeAreaView edges={['top']} style={styles.topSafeArea} />
      <GoBackHeader isGradientShow={false} />
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.card}>
          <Text style={styles.question}>Question {currentQuestionIndex + 1}:</Text>
          <Text style={styles.subText}>{currentQuestion.question}</Text>

          {currentQuestion.options.map(option => {
            const isNested = typeof option !== 'string';

            if (isNested) {
              const nested = option as NestedOption;

              const isParentSelected =
                currentQuestion.selectedAnswer === nested.label ||
                (nested.subOptions && nested.subOptions.includes(currentQuestion.selectedAnswer || ''));

              return (
                <View key={nested.label}>
                  <TouchableOpacity onPress={() => handleOptionSelect(nested.label)} style={styles.optionContainer}>
                    <View style={[styles.radioCircle, isParentSelected && {borderColor: '#4A48AD'}]}>
                      {isParentSelected && <View style={styles.selectedRb} />}
                    </View>
                    <Text style={styles.optionText}>{nested.label}</Text>
                  </TouchableOpacity>

                  {isParentSelected &&
                    nested.subOptions?.map(sub => (
                      <View key={sub} style={{paddingLeft: 30}}>
                        <TouchableOpacity onPress={() => handleOptionSelect(sub)} style={styles.optionContainer}>
                          <View
                            style={[
                              styles.radioCircle,
                              currentQuestion.selectedAnswer === sub && {borderColor: '#4A48AD'},
                            ]}>
                            {currentQuestion.selectedAnswer === sub && <View style={styles.selectedRb} />}
                          </View>
                          <Text style={styles.optionText}>{sub === 'Other' ? 'Other (please tell us)' : sub}</Text>
                        </TouchableOpacity>

                        {sub === 'Other' && nested.allowTextInput && currentQuestion.selectedAnswer === 'Other' && (
                          <TextInput
                            style={styles.input}
                            placeholder="Enter your answer"
                            value={currentQuestion.customText || ''}
                            onChangeText={handleCustomTextChange}
                          />
                        )}
                      </View>
                    ))}
                </View>
              );
            }

            return (
              <TouchableOpacity key={option} style={styles.optionContainer} onPress={() => handleOptionSelect(option)}>
                <View
                  style={[styles.radioCircle, currentQuestion.selectedAnswer === option && {borderColor: '#4A48AD'}]}>
                  {currentQuestion.selectedAnswer === option && <View style={styles.selectedRb} />}
                </View>
                <Text style={styles.optionText}>{option}</Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>

      <SafeAreaView edges={['bottom']} style={styles.bottomSafeArea}>
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.button, isContinueDisabled() && styles.buttonDisabled]}
            disabled={isContinueDisabled()}
            onPress={handleNext}>
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>
                {currentQuestionIndex === questionsState.length - 1 ? 'Finish' : 'Continue'}
              </Text>
            )}
          </TouchableOpacity>

          <View style={styles.dotsContainer}>
            {params.setting
              ? questionsState.map((_, i) => (
                  <View key={i} style={[styles.dot, currentQuestionIndex === i && styles.activeDot]} />
                ))
              : [...['', '', ''], ...questionsState].map((_, i) => (
                  <View key={i} style={[styles.dot, currentQuestionIndex + 3 === i && styles.activeDot]} />
                ))}
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topSafeArea: {
    backgroundColor: '#fefcf4',
  },
  bottomSafeArea: {
    backgroundColor: '#fff',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#fefcf4',
  },
  container: {
    flexGrow: 1,
    padding: 20,
    marginTop: Platform.OS == 'android' ? 40 : 25,
    justifyContent: 'space-between',
  },
  card: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  question: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  subText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 20,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#555',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRb: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4A48AD',
  },
  optionText: {
    marginLeft: 10,
    fontSize: 14,
  },
  input: {
    padding: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 15,
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: Platform.OS == 'android' ? 10 : 0
  },
  button: {
    backgroundColor: '#4A48AD',
    paddingVertical: 10,
    width: '100%',
    borderRadius: 20,
    marginBottom: 10,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#f9a826',
  },
});

export default OnboardingQuestion;
