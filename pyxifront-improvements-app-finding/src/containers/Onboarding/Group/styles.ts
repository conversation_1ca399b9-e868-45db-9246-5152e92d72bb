import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  box: {
    flex: 1,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 6,
    backgroundColor: '#fff',
    aspectRatio: 1,
  },

  text: {
    textAlignVertical: 'bottom',
    fontSize: 15,
    fontWeight: '500',
    color: '#1D1E20',
  },
  bottomButtonWrapper: {width: '100%', flex: 1, justifyContent: 'flex-end'},
  bottomButton: {
    width: '100%',
    height: 40,
    borderRadius: 6,
    // backgroundColor: !selectedItems.length ? '#F1F1F3' : '#FF9500',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomButtonText: {
    fontSize: 15,
    fontWeight: '500',
  },
});

export default styles;
