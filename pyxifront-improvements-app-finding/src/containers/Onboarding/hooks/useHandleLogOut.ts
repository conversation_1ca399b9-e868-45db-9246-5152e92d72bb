import OneSignal from 'react-native-onesignal';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';

export const useHandleLogOut = () => {
  const {reset} = useOnboardingStore();
  const {resetUser} = useUserStore();

  const handleLogOut = async () => {
    reset();
    resetUser();
    OneSignal.disablePush(true);
    await FirebaseAuth.logOut();
  };

  return {handleLogOut};
};
