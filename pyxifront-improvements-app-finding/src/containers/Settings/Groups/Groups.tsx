import React, { useState } from 'react';
import { View, ScrollView, TextInput, Alert, StyleSheet, Text, ViewStyle, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import Config from 'react-native-config';
import FirebaseAuth from '~services/FirebaseAuthService';
import { useNavigation } from '@react-navigation/native';
import { NavigationProps } from '~types/navigation/navigation.type';
import { useQuery } from 'react-query';
import { colors } from '~constants/colors';
import { fonts } from '~constants/fonts';
import { GoBackHeader } from '~components/GoBackHeader';
import Button from '~components/Button';
import Spinner from '~components/Spinner';
import { Notifier, NotifierComponents } from 'react-native-notifier';

interface Group {
    id: string;
    community_name: string;
    community_desc: string;
}

const Groups = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const [invitationCode, setInvitationCode] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  const { data: groups, isLoading, refetch } = useQuery<Group[]>(
    'joined-groups',
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: { Authorization: token, Accept: 'application/json' },
      };
      const response = await axios.get(Config.BASE_API_URL + 'community/joined', config);
      return response.data;
    }
  );

  const joinGroup = async () => {
    try {
      setIsJoining(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: { Authorization: token, Accept: 'application/json' },
      };
      await axios.post(
        Config.BASE_API_URL + 'community/join',
        { invitation_code: invitationCode },
        config
      );
      Notifier.showNotification({
        title: t('settings.join_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      setInvitationCode('');
      refetch();
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.join_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoining(false);
    }
  };

  const leaveGroup = async (groupId: string) => {
    try {
      setIsLeaving(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: { Authorization: token, Accept: 'application/json' },
      };
      await axios.delete(Config.BASE_API_URL + `community/leave/${groupId}`, config);
      Notifier.showNotification({
        title: t('settings.leave_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'info',
        },
      });
      refetch();
    } catch (error) {
        Notifier.showNotification({
            title: t('settings.leave_error'),
            Component: NotifierComponents.Alert,
            componentProps: {
              alertType: 'error',
            },
          });
      
    } finally {
      setIsLeaving(false);
    }
  };

  return (
    <View style={styles.container}>
      <GoBackHeader
        customText={t('settings.groups_management', 'Groups Management')}
        customCallback={() => navigation.goBack()}
      />
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>{t('settings.join_new_group', 'Join a New Group')}</Text>
          <Text style={styles.sectionInfo}>{t('settings.join_new_group_info', 'Join a New Group')}</Text>
          <TextInput
            style={styles.input}
            value={invitationCode}
            onChangeText={setInvitationCode}
            placeholder={t('settings.enter_invitation_code', 'Enter invitation code')}
            placeholderTextColor={colors.gray}
          />
          <Button
            label={t('settings.join_group', 'Join Group')}
            onPress={joinGroup}
            disabled={!invitationCode || isJoining}
            isLoading={isJoining}
            containerStyle={styles.joinButton}
          />
        </View>

        <View style={styles.card}>
          <Text style={styles.sectionTitle}>{t('settings.joined_groups', 'Your Groups')}</Text>
          
          {isLoading ? (
            <View style={styles.spinnerContainer}>
              <Spinner spinnerColor={colors.primary} />
            </View>
          ) : groups && groups.length > 0 ? (
            groups.map((group) => (
              <View key={group.id} style={styles.groupItem}>
                <Text style={styles.groupName}>{group.community_name}</Text>
                <TouchableOpacity 
                  style={[styles.leaveButton, isLeaving && styles.disabledButton]} 
                  onPress={() => leaveGroup(group.id)}
                  disabled={isLeaving}
                >
                  {isLeaving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.leaveButtonText}>
                      {t('settings.leave_group', 'Leave')}
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.noGroups}>
                {t('settings.no_groups', 'You haven\'t joined any groups yet')}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    marginTop: 70
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: fonts.medium,
    color: colors.black,
    marginBottom: 16,
  },
  sectionInfo: {
    fontSize: 12,
    fontFamily: fonts.medium,
    color: colors.black,
    marginBottom: 16,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 16,
    fontFamily: fonts.regular,
    color: colors.black,
    backgroundColor: '#F5F5F5',
  },
  joinButton: {
    marginTop: 8,
    backgroundColor: colors.primary,
    borderRadius: 8,
  } as ViewStyle,
  groupItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  groupName: {
    fontFamily: fonts.medium,
    fontSize: 16,
    color: colors.black,
    flex: 1,
  },
  leaveButton: {
    backgroundColor: colors.red,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  leaveButtonText: {
    color: colors.white,
    fontFamily: fonts.medium,
    fontSize: 14,
  },
  disabledButton: {
    opacity: 0.7,
  },
  emptyContainer: {
    paddingVertical: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noGroups: {
    fontFamily: fonts.regular,
    fontSize: 16,
    color: colors.gray,
    textAlign: 'center',
  },
  spinnerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 100,
  },
});

export default Groups;