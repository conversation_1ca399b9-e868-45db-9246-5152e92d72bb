import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, Text, View} from 'react-native';
import * as yup from 'yup';
import Button from '~components/Button/Button';
import {CustomTextInput} from '~components/CustomTextInput';
import {GoBackHeader} from '~components/GoBackHeader';
import styles from './styles';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import { logScreenView } from '~Utils/firebaseAnalytics';

const ChangePassword = () => {
  const navigation = useNavigation();
  const {t} = useTranslation();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Change Password', 'ChangePassword');
  }, []);

  const handlePasswordReset = async ({email}: {email: string}) => {
    try {
      auth().sendPasswordResetEmail(email.trim());
      navigation.goBack();
    } catch (error: any) {
      if (error.message) Alert.alert(error.message as string);
    }
  };

  const validationSchema = yup.object().shape({
    email: yup.string().required('Please enter event name').email('Enter valid email'),
  });

  return (
    <>
      <GoBackHeader />

      <KeyboardAvoidingView
        style={{paddingHorizontal: 16, flex: 1, marginTop: 80, marginBottom: Platform.OS === 'ios' ? 30 : 0}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <Text style={styles.headerText}>{t('settings.pwd_header')}</Text>
        <Text style={styles.requirementsText}>{t('settings.pwd_requirements')}</Text>
        <Formik
          validationSchema={validationSchema}
          onSubmit={handlePasswordReset}
          initialValues={{
            email: '',
          }}>
          {({values, handleChange, handleSubmit, errors}) => (
            <View style={styles.wrapper}>
              <View style={{flex: 1}} />
              <View style={{flex: 1}}>
                <CustomTextInput
                  value={values.email}
                  onChangeValue={handleChange('email')}
                  description={t('settings.email')}
                  errorText={errors.email?.toString()}
                />
              </View>
              <View style={{flex: 1, justifyContent: 'flex-end', paddingBottom: 16}}>
                <Button
                  label={t('generic.continue')}
                  isLoading={isLoading}
                  onPress={handleSubmit}
                  textStyle={{
                    color: '#fff',
                  }}
                  containerStyle={{
                    backgroundColor: '#FF9500',
                  }}
                />
              </View>
            </View>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </>
  );
};

export default ChangePassword;
