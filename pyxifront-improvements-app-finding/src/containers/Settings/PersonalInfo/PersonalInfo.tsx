import auth from '@react-native-firebase/auth';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import moment from 'moment-timezone';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as yup from 'yup';
import { logScreenView } from '~Utils/firebaseAnalytics';
import { reverseGeocode } from '~Utils/location';
import { CalendarIcon, DropdownArrowIcon } from '~assets/icons';
import Button from '~components/Button';
import CustomBox from '~components/CustomBox';
import { CustomTextInput } from '~components/CustomTextInput';
import DateTimeModal from '~components/DateTimeModal';
import EditProfileModal from '~components/EditProfileModal';
import { GoBackHeader } from '~components/GoBackHeader';
import LocationModal from '~components/LocationModal';
import { ModalWithGenders } from '~components/ModalWithItems';
import ChildrenQuantity from '~containers/Onboarding/Children/Children';
import SelectTypeOfProfile from '~containers/Onboarding/Group/Group';
import { useGetUserAccount } from '~hooks/user/useGetUser';
import { useGetUserGroups } from '~hooks/user/useGetUserGroups';
import { useUpdateUser } from '~hooks/user/useUpdateUser';

const PersonalInfo = () => {
  const {t} = useTranslation();
  const {data} = useGetUserAccount(auth().currentUser?.uid || '');
  const {data: userGroups} = useGetUserGroups();
  const {mutateAsync: handleUpdateUser} = useUpdateUser();
  // console.log('data', data);
  // const isFamilies = userGroups?.find(userGroup => userGroup.group_id === 1);

  const validationSchema = yup.object().shape({
    first_name: yup.string().required(t('settings.nameError')),
    lastName: yup.string().optional(),
    description: yup.string().required(t('settings.descriptionError')),
    birthday: yup.string().optional(),
    gender: yup.string().required(t('settings.addressError')),
    groups: yup.string().optional(),
  });

  const navigation = useNavigation();
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [isGenderModalOpen, setIsGenderModalOpen] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [isProfileTypeModalOpen, setIsProfileTypeModalOpen] = useState(false);
  const [isKidsModalOpen, setIsKidsModalOpen] = useState(false);
  const {bottom} = useSafeAreaInsets();
  const [birthdayDate, setBirthdayDate] = useState<moment.Moment | null>(null);
  const [addressName, setAddressName] = useState('');
  const [coords, setCoords] = useState({latitude: data?.coords?.lat, longitude: data?.coords?.long});

  const [locationError, setLocationError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const handleChooseBirthdayDate = useCallback((value?: Date) => {
    if (value) {
      setBirthdayDate(moment(value));
      return;
    }
  }, []);

  useEffect(() => {
    const func = async () => {
      if (data?.coords) {
        setCoords({latitude: data?.coords?.lat, longitude: data?.coords?.long});
        const address = await reverseGeocode({
          coords: {latitude: data?.coords?.lat, longitude: data?.coords?.long},
        });
        setAddressName(address || '');
        // console.log('coords', coords);
      }
    };
    func();
  }, [data]);

  useEffect(() => {
    logScreenView('Personal Info', 'PersonalInfo');
  }, []);

  return (
    <>
      <GoBackHeader />
      <View style={{flex: 1}}>
        <Formik
          onSubmit={async values => {
            setIsLoading(true);
            try {
              const inputDate = new Date(values.birthday);
              const timezoneNaiveDate = inputDate.toISOString().substring(0, 19);

              if (coords.latitude && coords.longitude) {
                const updatedUser = {
                  uid: auth().currentUser!.uid,
                  first_name: values.first_name,
                  last_name: values.lastName,
                  description: values.description,
                  coords: {lat: coords.latitude, long: coords.longitude},
                  gender: values.gender,
                  date_of_birth: timezoneNaiveDate,
                };
                await handleUpdateUser(updatedUser);
                navigation.goBack();
              }
            } catch (e) {
              setIsLoading(false);
            }
          }}
          initialValues={{
            first_name: data?.first_name || '',
            lastName: data?.last_name || '',
            description: data?.description || '',
            birthday: moment(data?.date_of_birth).toISOString() || '',
            gender: data?.gender || '',
            location: addressName || '',
            groups: userGroups?.map(group => group.group_id).toString() || '',
          }}
          validationSchema={validationSchema}>
          {({values, handleChange, handleSubmit, errors}) => {
            return (
              <>
                <ScrollView
                  style={{flex: 1}}
                  contentContainerStyle={{paddingHorizontal: 16, paddingBottom: 100, marginTop: 80}}>
                  <Text
                    style={{
                      lineHeight: 41,
                      fontSize: 34,
                      color: '#1D1E20',
                      fontWeight: '700',
                      marginTop: 12,
                      marginBottom: 16,
                    }}>
                    {t('settings.personalInfo')}
                  </Text>
                  <CustomTextInput
                    value={values.first_name}
                    onChangeValue={handleChange('first_name')}
                    description={t('settings.first_name')}
                    errorText={errors.first_name?.toString()}
                    placeholder={t('settings.name_placeholder')}
                  />
                  <CustomTextInput
                    value={values.lastName}
                    onChangeValue={handleChange('lastName')}
                    description={t('settings.last_name')}
                    errorText={errors.lastName?.toString()}
                    placeholder={t('settings.last_name_placeholder')}
                  />
                  <CustomTextInput
                    value={values.description}
                    onChangeValue={handleChange('description')}
                    description={t('settings.about_me')}
                    errorText={errors.description?.toString()}
                    placeholder="Please enter about your self"
                    isMultiline
                  />
                  <CustomBox
                    description={t('settings.birthday')}
                    handlePress={() => setIsDateModalOpen(true)}
                    icon={<CalendarIcon />}
                    value={values.birthday ? moment(values.birthday).format('DD/MM/YYYY') : ''}
                  />
                  <CustomBox
                    description={t('settings.gender1')}
                    value={t(`onboarding.${values.gender.split(' ')[0]}`)}
                    handlePress={() => setIsGenderModalOpen(true)}
                    icon={<DropdownArrowIcon />}
                    errorText={errors.gender}
                  />
                  <CustomBox
                    description={t('settings.location')}
                    handlePress={() => {
                      setIsLocationModalOpen(true);
                      setLocationError('');
                    }}
                    value={values.location || addressName}
                    errorText={locationError}
                  />
                  {/* <CustomBox
                    description={t('settings.group')}
                    handlePress={() => {
                      setIsProfileTypeModalOpen(true);
                    }}
                    value={
                      values.groups
                        .split(',')
                        ?.map(group => t(`onboarding.groups_${group}`))
                        .join(', ') || ''
                    }
                    errorText={typeOfProfileError}
                  /> */}
                  {/* {isFamilies && (
                  <CustomBox
                    description={t('settings.kids')}
                    handlePress={() => {
                      setIsKidsModalOpen(true);
                    }}
                    value={userChildren?.length.toString() || ''}
                  />
                )} */}
                </ScrollView>

                <Button
                  label={t('settings.save_changes')}
                  onPress={() => handleSubmit()}
                  isLoading={isLoading}
                  containerStyle={{
                    backgroundColor: '#FF9500',
                    marginHorizontal: 16,
                    marginBottom: Platform.OS === 'ios' ? bottom : 16,
                  }}
                  textStyle={{color: '#fff'}}
                />

                <DateTimeModal
                  isVisible={isDateModalOpen}
                  close={() => setIsDateModalOpen(false)}
                  onPress={() => {
                    if (birthdayDate) {
                      handleChange('birthday')(birthdayDate.format('YYYY-MM-DDTHH:mm:ss'));
                    }
                    setIsDateModalOpen(false);
                  }}
                  date={birthdayDate?.toDate() || moment().toDate()}
                  handleChooseMaxDate={handleChooseBirthdayDate}
                />
                <ModalWithGenders
                  chosenGender={values.gender}
                  isVisible={isGenderModalOpen}
                  close={() => setIsGenderModalOpen(false)}
                  onPress={value => () => {
                    handleChange('gender')(value);
                    setIsGenderModalOpen(false);
                  }}
                />
                <LocationModal
                  isVisible={isLocationModalOpen}
                  close={() => {
                    setIsLocationModalOpen(false);
                  }}
                  onLocationChange={object => {
                    if (object?.latitude && object.longitude) {
                      setCoords({latitude: object?.latitude, longitude: object?.longitude});
                      handleChange('location')(object?.address || '');
                    }
                  }}
                />
                <EditProfileModal isOpen={isProfileTypeModalOpen} onClose={() => setIsProfileTypeModalOpen(false)}>
                  <SelectTypeOfProfile
                    selectedGroups={values.groups.split(',').map(value => +value)}
                    isModal
                    isFromSettings
                    onSubmit={arr => {
                      handleChange('groups')(arr.toString());
                      setIsProfileTypeModalOpen(false);
                    }}
                  />
                </EditProfileModal>
                <EditProfileModal isOpen={isKidsModalOpen} onClose={() => setIsKidsModalOpen(false)}>
                  <ChildrenQuantity isFromSettings onSubmit={() => setIsKidsModalOpen(false)} />
                </EditProfileModal>
              </>
            );
          }}
        </Formik>
      </View>
    </>
  );
};

export default PersonalInfo;
