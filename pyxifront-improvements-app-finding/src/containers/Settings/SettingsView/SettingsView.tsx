import React, {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';
import {View, ScrollView, StatusBar, Platform, Text} from 'react-native';
import {
  SettingsHelpIcon,
  SettingsLogoutIcon,
  SettingsNotificationIcon,
  SettingsPasswordIcon,
  SettingsPersonIcon,
  LanguageIcon,
  SettingsEditInterests,
  SettingsEditPreferences,
} from '~assets/icons';
import {useTranslation} from 'react-i18next';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';
import Animated, {FadeInLeft} from 'react-native-reanimated';
import styles from './styles';
import {OptionItemProps} from '~types/settings/settings.type';
import {OptionItem, ProfileImageComponent} from '~components/Settings';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import OneSignal from 'react-native-onesignal';
import {useNotifications} from '~containers/Core/navigation/SettingsStack/zustand';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import DeviceInfo from 'react-native-device-info';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import {logScreenView} from '~Utils/firebaseAnalytics';

const SettingsView = () => {
  const {
    i18n: {language},
    t,
  } = useTranslation();
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {setIsNotificationsEnabled} = useNotifications();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {data: businessAccountData} = useGetBusinessAccount(auth()?.currentUser?.uid || '');
  const {data: userAccountData} = useGetUserAccount(auth().currentUser?.uid || '');
  const appVersion = DeviceInfo.getVersion();

  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const handleChangeUserImage = useCallback(
    async (value: string) => {
      const payload = {uid: auth().currentUser!.uid, photo: value};
      try {
        if (businessAccountData?.uid) {
          updateBusinessMutation(payload);
          return;
        }

        await updateUserMutation(payload);
      } catch (error) {
        console.log(error);
      }
    },
    [businessAccountData?.uid, updateBusinessMutation, updateUserMutation],
  );

  const isBusinessAccount = userType === 'business';
  const data: any = isBusinessAccount ? businessAccountData : userAccountData;

  const settings: OptionItemProps[] = useMemo(
    () => [
      {
        name: t('generic.language'),
        id: 'language',
        icon: LanguageIcon,
        subName: language,
        callback: () => setIsLanguagesModalVisible(true),
      },
      {
        name: t('settings.personal_info') ? t('settings.personal_info') : 'Personal Info',
        id: 'person',
        icon: SettingsPersonIcon,
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(isBusinessAccount ? SCREENS.CHANGE_PROFILE_INFO_BUSINESS : SCREENS.CHANGE_PROFILE_INFO);
        },
      },
      {
        name: t('settings.edit_interests'),
        id: 'subcategories',
        icon: SettingsEditInterests,
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.EDIT_SUBCATEGORIES);
        },
      },
      {
        name: t('settings.edit_preferance'),
        id: 'preferance',
        icon: SettingsEditPreferences,
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: true});
        },
      },
      {
        name: t('settings.purchase_history'),
        id: 'purchase_history',
        icon: require('~assets/icons/store.png'),
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.PURCHASE_HISTORY);
        },
      },
      {
        name: t('settings.change_pwd') ? t('settings.change_pwd') : 'Change Password',
        id: 'lock',
        icon: SettingsPasswordIcon,
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.CHANGE_PASSWORD);
        },
      },
      {
        name: t('settings.notifications') ? t('settings.notifications') : 'Notifications',
        id: 'notifications',
        icon: SettingsNotificationIcon,
        callback: () => {},
        isSwitch: true,
      },
      {
        name: t('events.neighbourhood_gropus'),
        id: 'groups',
        icon: require('~assets/icons/groups.png'),
        callback: () => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.GROUPS);
        },
      },
      {
        name: t('settings.help_center') ? t('settings.help_center') : 'Help Center',
        id: 'info',
        icon: SettingsHelpIcon,
        callback: () => {
          setIsTabBarDisabled(true);

          navigation.navigate(SCREENS.HELP_CENTER);
        },
      },
      {
        name: t('settings.sign_out') ? t('settings.sign_out') : 'Log out',
        id: 'logout',
        icon: SettingsLogoutIcon,
        callback: () => {
          setIsTabBarDisabled(true);

          navigation.navigate(SCREENS.LOGOUT);
        },
      },
    ],
    [language, setIsLanguagesModalVisible, t, navigation],
  );

  useLayoutEffect(() => {
    navigation.addListener('focus', () => {
      setIsTabBarDisabled(false);
    });
  }, []);

  useEffect(() => {
    logScreenView('Settings', 'SettingsView');
  }, []);

  useLayoutEffect(() => {
    (async () => {
      const state = await OneSignal.getDeviceState();
      setIsNotificationsEnabled(!state?.isPushDisabled);
    })();
  }, [setIsNotificationsEnabled]);

  return (
    <ScrollView style={styles.background} contentContainerStyle={styles.scrollViewContainer}>
      <ProfileImageComponent
        onChange={handleChangeUserImage}
        value={data?.photo}
        name={isBusinessAccount ? data?.name : `${data?.first_name} ${data?.last_name}`}
        email={data?.email}
      />
      <View style={styles.itemsWrapper}>
        {settings.map((setting, index) => (
          <Animated.View key={`${setting.id}-${index}`} entering={FadeInLeft.delay(100 * index + 50).duration(300)}>
            <OptionItem {...setting} />
          </Animated.View>
        ))}
      </View>
      <View style={{alignItems: 'center', marginTop: 8}}>
        <Text style={{color: 'gray'}}>Version: {appVersion}</Text>
      </View>
      <LanguagesModal isVisible={isLanguagesModalVisible} onClose={() => setIsLanguagesModalVisible(false)} />
    </ScrollView>
  );
};

export default SettingsView;
