import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {KeyboardAvoidingView, Platform, Text, View} from 'react-native';
import Button from '~components/Button';
import {CustomTextInput} from '~components/CustomTextInput';
import {GoBackHeader} from '~components/GoBackHeader';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import styles from './styles';

const PasswordForDeleteAccount = () => {
  const {navigate} = useNavigation<NavigationProps>();
  const {t} = useTranslation();

  const [pas, setPas] = useState('');
  const [pasError, setPasError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const onSubmit = () => {
    setIsLoading(true);
    const user = auth().currentUser;
    const credential = auth.EmailAuthProvider.credential(user?.email!, pas);
    user
      ?.reauthenticateWithCredential(credential)
      .then(() => {
        setPasError('');
        navigate(SCREENS.DELETE_LOADING);
      })
      .catch(() => {
        setIsLoading(false);
        setPasError('Invalid password');
      });
  };

  return (
    <>
      <GoBackHeader />
      <KeyboardAvoidingView
        style={{paddingHorizontal: 16, flex: 1, marginTop: 80, marginBottom: Platform.OS === 'ios' ? 30 : 0}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <Text style={styles.headerText}>{'Delete Account'}</Text>
        <Text style={styles.requirementsText}>{'Please enter your password to delete the account'}</Text>
        <View style={{flex: 1}}></View>
        <View style={{flex: 1}}>
          <CustomTextInput
            isPassword
            description={t('signin.password')}
            onChangeValue={value => {
              setPas(value);
            }}
            value={pas}
            errorText={pasError}
          />
        </View>

        <View style={{flex: 1, paddingBottom: 16, justifyContent: 'flex-end'}}>
          <Button
            containerStyle={{backgroundColor: 'orange'}}
            isLoading={isLoading}
            textStyle={{color: 'white'}}
            label={t('settings.delete_account')}
            onPress={onSubmit}
          />
        </View>
      </KeyboardAvoidingView>
    </>
  );
};

export default PasswordForDeleteAccount;
