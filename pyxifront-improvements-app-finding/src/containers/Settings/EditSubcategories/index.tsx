import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useLayoutEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, TextInput, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {SubCategoryType} from '~types/categories';
import styles from './styles';
import { logScreenView } from '~Utils/firebaseAnalytics';

const isAndroid = Platform.OS === 'android';
const ANDROID_MARGIN_BOTTOM = 20;
const EMPTY_LIST_BUTTON_BACKGROUND = '#FF9500';
const LIST_BUTTON_BACKGROUND = '#F1F1F3';

const EditSubcategories = () => {
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {data: userSubcategories, isLoading: isUserSabcategoriesLoading} = useGetUserSubcategories(
    auth().currentUser?.uid || '',
  );
  const {goBack} = useNavigation();
  const {setIsTabBarDisabled} = useTabBar();

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    if (d && data && !searchValue) {
      setCategories(organizeByCategory([], d, data));
    }
  }, [d, data, searchValue]);

  useEffect(() => {
    if (userSubcategories) {
      setSelectedSubcategories(userSubcategories.map(item => item.subcategory_id));
    }
  }, [userSubcategories]);

  useEffect(() => {
    logScreenView('Edit Subcategories', 'EditSubcategories');
  }, []);

  const submit = async () => {
    setIsLoading(true);
    try {
      await updateUserSubcategories({ids: selectedSubcategories});
      goBack();
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  useEffect(() => {
    handleSearchCategory()
  }, [searchValue])

  const handleSearchCategory = () => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      setCategories(organizeByCategory([], d, inputData));
    } else if (data && d) {
      setCategories(organizeByCategory([], d, data));
    }
  };

  return (
    <>
      <GoBackHeader />
      <View style={styles.container}>
        <View style={{width: '100%', alignItems: 'flex-start', paddingHorizontal: 16}}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: 24,
              color: '#1D1E20',
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('settings.edit_interests')}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 16,
          }}>
          <TextInput
            placeholder="Search by name"
            value={searchValue}
            onChangeText={setSearchValue}
            style={{
              flex: 1,
              height: 40,
              borderWidth: 1,
              borderColor: '#888',
              borderRadius: 5,
              paddingHorizontal: 10,
              marginRight: 8,
            }}
          />
          <Button
            label="Search"
            onPress={handleSearchCategory}
            containerStyle={{backgroundColor: '#FF9500', height: 40, borderRadius: 5}}
            textStyle={{color: 'white', fontWeight: '600'}}
          />
        </View>

        <View
          style={{
            flex: 1,
            width: '100%',
          }}>
          <SubcategoriesLists
            categoriesList={d}
            searchValue={searchValue} 
            filteredSubcategoriesData={isUserSabcategoriesLoading ? null : categories}
            selectedCategories={selectedSubcategories}
            onCategoryChange={value => {
              setSelectedSubcategories(prevState =>
                prevState.includes(value) ? prevState.filter(item => item !== value) : [...prevState, value],
              );
            }}
          />
        </View>

        <View style={[styles.footer, {marginBottom: isAndroid ? ANDROID_MARGIN_BOTTOM : bottom}]}>
          <Button
            disabled={!selectedSubcategories.length}
            isLoading={isLoading}
            label={t('generic.submit')}
            onPress={submit}
            textStyle={styles.buttonTextStyle}
            containerStyle={{
              backgroundColor: !selectedSubcategories?.length ? LIST_BUTTON_BACKGROUND : EMPTY_LIST_BUTTON_BACKGROUND,
              ...styles.buttonContainerStyle,
            }}
          />
        </View>
      </View>
    </>
  );
};

export default EditSubcategories;
