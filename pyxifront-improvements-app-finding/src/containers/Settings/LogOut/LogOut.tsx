import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import OneSignal from 'react-native-onesignal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {useMapsContext} from '~providers/maps/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import { logScreenView } from '~Utils/firebaseAnalytics';

const LogOut = () => {
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {goBack} = useNavigation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {resetMapsState} = useMapsContext();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {resetUser} = useUserStore();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Logout', 'LogOut');
  }, []);

  return (
    <FastImage
      source={require('~assets/images/mainSplashImage.jpg')}
      resizeMode="cover"
      style={{width: '100%', height: '100%'}}>
      <View style={{flex: 1, backgroundColor: 'rgba(0,0,0,.5)', paddingHorizontal: 16}}>
        <View style={{flex: 1}} />
        <View style={{flex: 1}}>
          <Text style={{fontSize: 24, fontWeight: '400', color: 'white', textAlign: 'center'}}>
            {t('settings.sign_out_body')}
          </Text>
        </View>
        <View style={{flex: 1, paddingBottom: Platform.OS === 'ios' ? bottom : 16, justifyContent: 'flex-end'}}>
          <Button
            onPress={async () => {
              setIsLoading(true);
              OneSignal.disablePush(true);
              if (userType === 'personal') {
                await updateUserMutation({
                  coords_real: null,
                });
              }
              resetMapsState();
              resetUser();
              await FirebaseAuth.logOut();
            }}
            isLoading={isLoading}
            label={t('settings.sign_out')}
            containerStyle={{backgroundColor: 'red'}}
            textStyle={{color: 'white', fontWeight: '600', fontSize: 16}}
          />
          <Button
            onPress={goBack}
            label={t('generic.cancel')}
            containerStyle={{borderWidth: 1, borderColor: 'red', marginTop: 16}}
            textStyle={{color: 'red', fontWeight: '600', fontSize: 16}}
          />
        </View>
      </View>
    </FastImage>
  );
};

export default LogOut;
