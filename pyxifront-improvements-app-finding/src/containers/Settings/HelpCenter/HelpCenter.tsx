import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Text, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {contactUs} from '~Utils/Settings';
import Button from '~components/Button/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
// import { useChatStore } from '~providers/chats/zustand';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import OneSignal from 'react-native-onesignal';
import {useMapsContext} from '~providers/maps/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import styles from './styles';
import { SCREENS } from '~constants';
import { logScreenView } from '~Utils/firebaseAnalytics';

const HelpCenter = () => {
  const {navigate} = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  // const {clearState} = useChatStore();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {bottom} = useSafeAreaInsets();
  const {goBack} = useNavigation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {resetMapsState} = useMapsContext();
  const {resetUser} = useUserStore();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    console.log(auth().currentUser?.providerData[0].providerId);
    
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Help Center', 'HelpCenter');
  }, []);

  const deleteAccount = async () => {
    if (auth().currentUser?.providerData[0].providerId == 'password') {
      navigate(SCREENS.PASSWORD_FOR_DELETE_ACCOUNT);
    } else {
      navigate(SCREENS.DELETE_LOADING);
    }
    return;
    setIsLoading(true);
    const currentUser = auth().currentUser;
    currentUser
      ?.delete()
      .then(async () => {
        OneSignal.disablePush(true);
        if (userType === 'personal') {
          await updateUserMutation({
            coords_real: null,
          });
        }
        resetMapsState();
        resetUser();
        await FirebaseAuth.logOut();
        setIsLoading(false);
      })
      .catch((error) => {
        console.log(error);
        
        Alert.alert(error.message ? error.message : "Failed to delete account.");
        setIsLoading(false);
      });

    // const providerId = auth().currentUser?.providerData[0].providerId;
    // if (providerId === 'google.com' || providerId === 'apple.com') {
    //   navigation.navigate(SCREENS.DELETE_LOADING);
    // } else {
    //   navigation.navigate(SCREENS.PASSWORD_FOR_DELETE_ACCOUNT);
    // }
  };

  const handleDeleteAccountAlert = async () => {
    Alert.alert(t('settings.deleteAccountAlert.title'), t('settings.deleteAccountAlert.description'), [
      {onPress: deleteAccount, text: t('settings.deleteAccountAlert.delete'), style: 'destructive'},
      {text: t('settings.deleteAccountAlert.cancel'), style: 'cancel', isPreferred: true},
    ]);
  };

  return (
    <>
      <GoBackHeader />

      <KeyboardAwareScrollView style={styles.container} contentContainerStyle={styles.mainWrapper} bounces={false}>
        <Text style={styles.headerText}>{t('settings.contact_us')}</Text>
        <Text style={styles.requirementsText}>{t('settings.contact_us_body')}</Text>

        <View style={styles.wrapper}>
          <Button
            label={t('settings.delete_account')}
            isLoading={isLoading}
            onPress={handleDeleteAccountAlert}
            containerStyle={{backgroundColor: '#fff', borderWidth: 1, borderColor: '#FF9500'}}
            textStyle={{color: '#FF9500'}}
            spinnerColor="#FF9500"
          />
          <Button
            label={t('settings.contact_us')}
            onPress={contactUs}
            containerStyle={{backgroundColor: '#FF9500', marginTop: 24}}
            textStyle={{color: '#fff'}}
          />
        </View>
      </KeyboardAwareScrollView>
    </>
  );
};

export default HelpCenter;
