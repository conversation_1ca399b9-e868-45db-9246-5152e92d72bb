import * as React from 'react';
import {useEffect, useState} from 'react';
import {Appearance, Image, PermissionsAndroid, Platform, Text, View} from 'react-native';
import Config from 'react-native-config';
import FastImage from 'react-native-fast-image';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import OneSignal from 'react-native-onesignal';
import {QueryClient, QueryClientProvider} from 'react-query';
import '~i18n';
import Navigation from './navigation/Navigation';
import {useLanguage} from './navigation/SettingsStack/zustand';
// import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import {useTranslation} from 'react-i18next';
import {StatusBar} from 'react-native';
import {KeyboardProvider} from 'react-native-keyboard-controller';
import {NotifierWrapper} from 'react-native-notifier';
import SplashScreen from 'react-native-splash-screen';
import {getFcmToken} from '~Utils/fcmHelper';
import FirebaseChatsService from '~services/FirebaseChats';
import {StripeProvider} from '@stripe/stripe-react-native';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import './../../Utils/fetchIntercept';

// Sentry.init({
//   dsn: 'https://<EMAIL>/4505561115131904',
//   debug: false,
//   release: DeviceInfo.getApplicationName() + '@' + DeviceInfo.getVersion() + '(' + DeviceInfo.getBuildNumber() + ')',
//   tracesSampleRate: 1,
// });
const queryClient = new QueryClient();

const isAndroid = Platform.OS === 'android';

export default function App() {
  if (!isAndroid) {
    OneSignal.promptForPushNotificationsWithUserResponse();
  }
  OneSignal.setLogLevel(6, 0);
  OneSignal.setAppId(Config.ONE_SIGNAL_APP_ID);
  //END OneSignal Init Code
  //Prompt for push on iOS

  const {i18n} = useTranslation();
  const {setLanguage, language} = useLanguage();

  const [darkTheme, setDarkTheme] = useState(false);

  useEffect(() => {
    // Subscribe to theme changes
    const themeListener = Appearance.addChangeListener(({colorScheme}) => {
      setDarkTheme(colorScheme === 'dark');
    });

    // Cleanup listener
    return () => themeListener.remove();
  }, []);

  useEffect(() => {
    const fetchAndSaveFcmToken = async () => {
      const deviceFcmToken = await getFcmToken();
      console.log('deviceFcmToken', deviceFcmToken);
      if (deviceFcmToken) {
        await FirebaseChatsService.updateUserFcmToken(deviceFcmToken);
      }
    };

    fetchAndSaveFcmToken();
  }, []);

  //Method for handling notifications received while app in foreground
  // Handle notifications received while the app is in the foreground
  // useEffect(() => {
  //   handleForegroundNotification();
  // }, []);

  //Method for handling notifications opened
  useEffect(() => {
    const images = [
      require('~assets/images/personalImage.jpg'),
      require('~assets/images/businessImage.jpg'),
      require('~assets/images/SignInImage.png'),
      require('~assets/images/eventPhotoPlaceholder.png'),
      require('~assets/images/mainSplashImage.jpg'),
    ];
    const uris = images.map(image => ({
      uri: Image.resolveAssetSource(image).uri,
    }));

    FastImage.preload(uris);
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      SplashScreen.hide();
    }, 200);
    return () => clearTimeout(timeout);
  }, []);

  React.useEffect(() => {
    if (Platform.OS === 'android') {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_CALENDAR, {
        title: 'Calendar Permission',
        message: 'App needs access to your calendar to create events.',
        buttonPositive: 'OK',
      });
    }
  }, []);

  useEffect(() => {
    AsyncStorage.getItem('language-storage').then((item: any) => {
      const locale = JSON.parse(item).state?.language;
      i18n.changeLanguage(locale);
      setLanguage(locale);
    });
  }, [language]);

  useEffect(() => {
    Appearance.setColorScheme('light');
  }, []);
  // useEffect(() => {
  //   if (userLocation?.coords?.latitude && userLocation?.coords?.longitude && userLocation?.locationName) {
  //     setCurrentPositionState({
  //       latitude: userLocation.coords.latitude,
  //       longitude: userLocation.coords.longitude,
  //       address: userLocation.locationName,
  //     });
  //   }
  // }, [userLocation?.locationName, userLocation?.coords.longitude, userLocation?.coords.latitude]);

  return (
    <KeyboardProvider>
      <StripeProvider publishableKey={Config.STRIPE_PUBLISH_KEY}>
        <QueryClientProvider client={queryClient}>
          <GestureHandlerRootView style={{flex: 1}}>
            <NotifierWrapper>
              <Navigation />
            </NotifierWrapper>
          </GestureHandlerRootView>
        </QueryClientProvider>
      </StripeProvider>
    </KeyboardProvider>
  );
}
