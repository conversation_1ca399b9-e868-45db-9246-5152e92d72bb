import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {settingsScreens} from '../screens';
import {useEffect} from 'react';
import {SCREENS} from '~constants';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';

const Stack = createNativeStackNavigator();

export const SettingsStack = () => {
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.SETTINGS_STACK>>();
  const {navigate, setParams} = useNavigation<NavigationProps>();

  useEffect(() => {
    if (params?.refineSubcategories) {
      navigate(SCREENS.EDIT_SUBCATEGORIES);
      setParams({});
    }
  }, [navigate, params?.refineSubcategories, setParams]);

  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      {settingsScreens.map(screen => (
        <Stack.Screen name={screen.name} key={screen.name} component={screen.component} options={screen.options} />
      ))}
    </Stack.Navigator>
  );
};
