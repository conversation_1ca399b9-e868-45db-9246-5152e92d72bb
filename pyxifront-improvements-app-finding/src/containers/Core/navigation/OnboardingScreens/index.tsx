import React, {useEffect} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {onboardingScreens} from '../screens';
import {useUserStore} from '~providers/userStore/zustand';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';

const Stack = createNativeStackNavigator();

const OnboardingScreens = () => {
  const {user} = useUserStore();
  const {navigate} = useNavigation<NavigationProps>();

  useEffect(() => {
    if (!user) {
      return;
    }
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
      return;
    } else if (!user?.is_registration_finished) {
      if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
        navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        return;
      } else {
        if (!user.photo) {
          navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
          return;
        } else {
          navigate(SCREENS.ONBOARDING_GROUP);
        }
      }
    }
  }, [user, navigate]);

  return (
    <Stack.Navigator initialRouteName={user ? SCREENS.ONBOARDING_GROUP : undefined}>
      {onboardingScreens.map(screen => {
        return (
          <Stack.Screen key={screen.name} name={screen.name} component={screen.component} options={screen.options} />
        );
      })}
    </Stack.Navigator>
  );
};

export default OnboardingScreens;
