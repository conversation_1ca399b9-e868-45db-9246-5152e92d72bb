import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {homeScreens} from '../screens';
import AnimatedLoadingModal from '~components/Matching/AnimatedLoadingModal';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {useEffect} from 'react';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';

const Stack = createNativeStackNavigator();

export const HomeStack = () => {
  const {isMatchingLoadingModalVisible, matchingModalCallback, noMatchesButtonIsVisible, closeMatchingLoadingModal} =
    useMatchingLoadingModalAnimation();
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.HOME_STACK>>();
  const {navigate, setParams} = useNavigation<NavigationProps>();

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!params?.eventId) {
        return;
      }

      navigate(SCREENS.HOME_EVENT, {eventId: params.eventId});
      setParams({eventId: undefined});
    }, 400);

    return () => clearTimeout(timeout);
  }, [navigate, setParams, params?.eventId]);

  return (
    <>
      <Stack.Navigator screenOptions={{headerShown: false}}>
        {homeScreens.map(screen => (
          <Stack.Screen name={screen.name} key={screen.name} component={screen.component} options={screen.options} initialParams={params} />
        ))}
      </Stack.Navigator>
      <AnimatedLoadingModal
        isVisible={isMatchingLoadingModalVisible}
        callback={matchingModalCallback}
        isButtonVisible={noMatchesButtonIsVisible}
        close={closeMatchingLoadingModal}
      />
    </>
  );
};
