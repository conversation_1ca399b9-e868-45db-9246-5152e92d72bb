import {Navigation<PERSON>ontainer, NavigationContainerRef} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import SCREENS from '~constants/screens';
import AppScreens from './AppScreens/AppScreens';
import AuthScreens from './AuthScreens';
import auth from '@react-native-firebase/auth';
import OnboardingScreens from './OnboardingScreens';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import LoadingScreen from '~containers/Loading';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useMapsContext} from '~providers/maps/zustand';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUserStore} from '~providers/userStore/zustand';
import VersionCheck from 'react-native-version-check';
import {Alert, Linking, Platform, StatusBar} from 'react-native';
import {useTranslation} from 'react-i18next';
import ChatProvider from '~providers/chats/context';
import axios from 'axios';

// 5 mins
const SECONDS_TO_REFETCH_LOCATION = 300;
export const navigationRef = React.createRef<NavigationContainerRef<any>>();
const Navigation = () => {
  const {t} = useTranslation();
  const {setCurrentPositionState, currentPositionState} = useMapsContext();
  const {getCurrentPosition, getLocationName} = useGetCurrentPosition();
  const Stack = createNativeStackNavigator();
  const [isAuth, setIsAuth] = useState(!!auth().currentUser);
  const {data: userType, isLoading: userTypeIsLoading} = useGetUserType(auth().currentUser?.uid || '');

  const {data, isLoading: userAccountIsLoading} = useGetUserAccount(auth()?.currentUser?.uid || '');
  const {data: businessData, isLoading: businessIsLoading} = useGetBusinessAccount(auth()?.currentUser?.uid || '');

  const [isNeedToUpdate, setIsNeedToUpdate] = useState(true);

  const {setUser, setBusiness} = useUserStore();

  const isBusiness = userType === 'business';

  const infiniteAlert = useCallback(
    (url: string) => {
      Alert.alert(t('settings.update_your_Pyxi'), '', [
        {
          text: 'Go to store',
          onPress: () => {
            infiniteAlert(url);
            Linking.openURL(url);
          },
        },
      ]);
    },
    [t],
  );

  const getIOSProvider = async (appStoreURL: string) => {
    try {
      const response = await axios.get(appStoreURL, {
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X)',
        },
      });
      const data = response.data;
      return data?.resultCount > 0 ? data.results : null;
    } catch (error) {
      return null;
    }
  };

  const checkVersionAndAskUpdate = async () => {
    if (Platform.OS == 'android') {
      VersionCheck.needUpdate().then(async res => {
        console.log(res, 'VersionCheck');

        setIsNeedToUpdate(res?.isNeeded);
        if (res?.isNeeded) {
          infiniteAlert(res.storeUrl);
        }
      });
    } else {
      const country = await VersionCheck.getCountry();
      const packageName = VersionCheck.getPackageName();
      const appStoreURL = `https://itunes.apple.com/${country}/lookup?bundleId=${packageName}`; // iTunes API URL of your package
      console.log(appStoreURL, 'appStoreURLappStoreURL');

      getIOSProvider(appStoreURL).then(res => {
        // call method
        console.log(res[0].version, 'getIOSProvidergetIOSProvider');
        if (res[0]) {
          if (res[0].version) {
            VersionCheck.needUpdate({
              currentVersion: VersionCheck.getCurrentVersion(), // this returns the version setted on the last build
              latestVersion: res[0].version,
            }).then(resNeedUpdate => {
              console.log(resNeedUpdate, 'resNeedUpdate');
              setIsNeedToUpdate(resNeedUpdate?.isNeeded);
              if (resNeedUpdate?.isNeeded) {
                infiniteAlert(res[0].trackViewUrl);
              }
            });
          }
        }
      });
    }
  };

  useEffect(() => {
    checkVersionAndAskUpdate();
  }, [infiniteAlert]);

  useEffect(() => {
    if (businessData) {
      setBusiness(businessData);
    }

    if (data && !(data as unknown as {detail: string}).detail) {
      setUser(data);
    }
  }, [data, businessData, setBusiness, setUser]);

  useEffect(() => {
    auth().onAuthStateChanged(user => {
      if (user) {
        setIsAuth(true);
      } else {
        setIsAuth(false);
      }
    });
  }, []);

  useEffect(() => {
    if (isBusiness) {
      return;
    }

    let interval = setInterval(() => getCurrentPosition(true), SECONDS_TO_REFETCH_LOCATION * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [getCurrentPosition, isBusiness]);

  const fetchUserPosition = useCallback(async () => {
    if (!currentPositionState && data?.coords.lat && data?.coords.long) {
      if (data?.coords_real?.lat && data?.coords_real?.long) {
        const locationName = await getLocationName(data.coords_real.lat, data.coords_real.long);

        setCurrentPositionState({
          latitude: data.coords_real.lat,
          longitude: data.coords_real.long,
          address: locationName || '',
        });
      } else {
        const locationName = await getLocationName(data.coords.lat, data.coords.long);

        setCurrentPositionState({
          latitude: data?.coords_real?.lat,
          longitude: data?.coords_real?.long,
          address: locationName || '',
        });
      }

      getCurrentPosition(true, true);
    }
  }, [
    currentPositionState,
    data?.coords_real?.lat,
    data?.coords_real?.long,
    data?.coords?.lat,
    data?.coords?.long,
    getCurrentPosition,
    setCurrentPositionState,
    getLocationName,
  ]);

  const fetchBusinessPosition = useCallback(async () => {
    if (!currentPositionState && businessData?.coords.lat && businessData?.coords.long) {
      if (businessData?.coords_real?.lat && businessData?.coords_real?.long) {
        const locationName = await getLocationName(businessData.coords_real.lat, businessData.coords_real.long);

        setCurrentPositionState({
          latitude: businessData.coords_real.lat,
          longitude: businessData.coords_real.long,
          address: locationName || '',
        });
      } else {
        const locationName = await getLocationName(businessData.coords.lat, businessData.coords.long);

        setCurrentPositionState({
          latitude: businessData?.coords_real?.lat,
          longitude: businessData?.coords_real?.long,
          address: locationName || '',
        });
      }
    }
  }, [
    businessData?.coords.lat,
    businessData?.coords.long,
    businessData?.coords_real?.lat,
    businessData?.coords_real?.long,
    currentPositionState,
    getLocationName,
    setCurrentPositionState,
  ]);

  useEffect(() => {
    if (isBusiness) {
      fetchBusinessPosition();
    } else {
      fetchUserPosition();
    }
  }, [fetchUserPosition, fetchBusinessPosition, isBusiness]);

  const loadingIsActive = useMemo(() => {
    if (!isAuth) {
      return false;
    }

    if (userTypeIsLoading) {
      return true;
    }

    if (userType === 'business' && businessIsLoading) {
      return true;
    } else if (userType === 'business' && !businessIsLoading) {
      return false;
    }

    if (userType === 'personal' && userAccountIsLoading) {
      return true;
    } else if (userType === 'personal' && !userAccountIsLoading) {
      return false;
    }
    return false;
  }, [isAuth, businessIsLoading, userAccountIsLoading, userType, userTypeIsLoading]);

  const screens = useMemo(() => {
    if (loadingIsActive || isNeedToUpdate) {
      return <Stack.Screen name={SCREENS.LOADING} component={LoadingScreen} />;
    }
    if (!isAuth || !auth().currentUser?.emailVerified) {
      return <Stack.Screen name={SCREENS.AUTH} component={AuthScreens} />;
    }

    if (!data?.is_registration_finished && !businessData?.is_registration_finished) {
      return <Stack.Screen name={SCREENS.ONBOARDING} component={OnboardingScreens} />;
    }

    if (!data?.onboarding_answers || data?.onboarding_answers.length == 0) {
      return <Stack.Screen name={SCREENS.ONBOARDING} component={OnboardingScreens} />;
    }
    return <Stack.Screen name={SCREENS.APP_ROOT} component={AppScreens} />;
  }, [
    loadingIsActive,
    isAuth,
    data?.is_registration_finished,
    businessData?.is_registration_finished,
    Stack,
    isNeedToUpdate,
  ]);

  return (
    <NavigationContainer ref={navigationRef}>
      <ChatProvider>
        <StatusBar barStyle={'dark-content'} />
        <Stack.Navigator screenOptions={{headerShown: false, gestureEnabled: false, animation: 'default'}}>
          {screens}
        </Stack.Navigator>
      </ChatProvider>
    </NavigationContainer>
  );
};

export default Navigation;
