import {Platform} from 'react-native';
import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';

interface State {
  isTabBarDisabled: boolean;
}

interface Actions {
  setIsTabBarDisabled: (value: boolean) => void;
}

const useTabBar = create<State & Actions>()(
  immer(set => ({
    isTabBarDisabled: false,
    setIsTabBarDisabled: (name: boolean) => {
      Platform.OS === 'android'
        ? setTimeout(() => {
            set(state => {
              state.isTabBarDisabled = name;
            });
          }, 150)
        : set(state => {
            state.isTabBarDisabled = name;
          });
    },
  })),
);

export default useTabBar;
