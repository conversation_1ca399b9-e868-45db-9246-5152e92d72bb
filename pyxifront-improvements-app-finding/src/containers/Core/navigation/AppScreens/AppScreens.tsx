import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Platform, Text, View, StyleSheet, Linking} from 'react-native';

import {SCREENS} from '~constants';
import {appScreens} from '../screens';
import {AnimatedTabBarNavigator, TabElementDisplayOptions} from 'react-native-animated-nav-tab-bar';
import useTabBar from './zustand';
import {useTranslation} from 'react-i18next';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import OneSignal from 'react-native-onesignal';
import {registerListenerWithFCM} from '~Utils/fcmHelper';
import useDeepLinking from './useDeepLinking';
import firestore from '@react-native-firebase/firestore';
import {ChatType, ChatTypeWithKey} from '~types/chat';

const Tabs = AnimatedTabBarNavigator();

const AppScreens = () => {
  const {isTabBarDisabled} = useTabBar();
  const uid = auth().currentUser?.uid;
  const {t} = useTranslation();
  useDeepLinking();
  const {refetch} = useGetUserAccount(auth().currentUser!.uid);
  const {navigate} = useNavigation<NavigationProps>();
  const [unreadChatCount, setUnReadChatCount] = useState<number | null>(null);

  useEffect(() => {
    if (uid) {
      const unsubscribe = firestore()
        .collection('chats')
        .where('userIds', 'array-contains', uid)
        .onSnapshot(querySnapshot => {
          const conversations: ChatTypeWithKey[] = [];
          let unReadCount = 0;
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data() as ChatType; // Cast Firestore data to our ChatData type
            conversations.push({
              ...data,
              key: documentSnapshot.id,
            });
            const unread = data?.history.filter(
              message => message?.readUserIds && !message.readUserIds.includes(uid),
            ).length;
            unReadCount = unReadCount + unread;
          });
          setUnReadChatCount(unReadCount);
        });

      // Clean up subscription on unmount
      return () => unsubscribe();
    }
  }, [uid]);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);

  const openEvent = useCallback(
    async (eventID: string) => {
      const data = await refetch();
      console.log(eventID);

      if (data?.data) {
        navigate(SCREENS.HOME_STACK, {eventId: Number(eventID)});
        return;
      }
      Alert.alert('No permission to event');
    },
    [navigate, refetch],
  );

  useEffect(() => {
    const handleDeepLink = ({url}: {url: string}) => {
      console.log(url);
      const parts = url.split('/').filter(item => item);
      console.log(parts);
      const eventID = parts[parts.length - 1];
      console.log(eventID);
      openEvent(eventID);
    };
    // Check for the initial deep link when the app is launched
    const checkInitialURL = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        console.log(`Initial deep link URL: ${initialUrl}`);
        handleDeepLink({url: initialUrl});
      }
    };

    // Add an event listener for deep linking when the app is already running
    const linkingListener = Linking.addListener('url', handleDeepLink);

    checkInitialURL();

    return () => {
      // Clean up the event listeners
      linkingListener.remove();
    };
  }, []);

  const openChat = useCallback(
    async ({chatId, chatType}: {chatId: string; chatType: string}) => {
      const data = await refetch();
      if (data?.data) {
        navigate(SCREENS.CHAT_STACK, {key: chatId, chatType});
        return;
      }
      Alert.alert('No permission to chat');
    },
    [navigate, refetch],
  );

  OneSignal.setNotificationOpenedHandler(notification => {
    const additionalData = notification.notification.additionalData as {
      type?: string;
      chat_id?: string;
      event_id?: string;
      refineSubcategories?: boolean;
    };
    console.log(additionalData, 'additionalDataadditionalData');
    if (additionalData?.chat_id || additionalData?.type === 'AdminChat') {
      openChat({
        chatId: additionalData?.chat_id || '',
        chatType: additionalData?.type || '',
      });
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_UPDATED') {
      navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_COMMENT') {
      navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_CANCELLED') {
      navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REQUEST_UPDATED') {
      navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REMINDER') {
      navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
    } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REQUESTING') {
      navigate(SCREENS.PENDING_ATTENDEES, {eventId: Number(additionalData.event_id), eventName: 'Event'});
    } else if (additionalData?.type === 'EVENT_DELETED') {
      return;
    } else if (additionalData?.refineSubcategories) {
      navigate(SCREENS.SETTINGS_STACK, {refineSubcategories: additionalData?.refineSubcategories});
    } else {
      return;
    }
  });

  return (
    <Tabs.Navigator
      screenOptions={{
        unmountOnBlur: true,
        lazy: false,
      }}
      tabBarOptions={{
        activeTintColor: '#ffffff',
        inactiveTintColor: '#ffffff',
        activeBackgroundColor: 'black',
      }}
      appearance={{
        isDisabled: isTabBarDisabled,
        floating: true,
        tabBarBackground: Platform.OS === 'ios' ? 'rgba(189, 190, 190,0.5)' : 'rgba(255,255,255,0.3)',
        whenActiveShow: TabElementDisplayOptions.ICON_ONLY,
      }}
      initialRouteName={SCREENS.HOME_STACK}>
      {appScreens.map(screen => (
        <Tabs.Screen
          component={screen.component}
          key={screen.name}
          name={screen.name}
          listeners={() => ({})}
          initialParams={screen.params}
          options={{
            tabBarIcon: ({color, focused}: {color: string; focused: boolean}) => (
              <View style={styles.iconContainer}>
                <View style={[styles.iconWrapper, focused && styles.focusedIconWrapper]}>
                  {focused ? <screen.iconBar color={color} /> : <screen.icon color={color} />}
                  {/* Add the Badge with Unread Count */}
                  {!!unreadChatCount && screen.name == 'SCREEN:CHAT_STACK' && (
                    <View style={styles.badgeContainer}>
                      <Text style={styles.badgeText}>{unreadChatCount + ''}</Text>
                    </View>
                  )}
                </View>
                {focused && <Text style={styles.iconText}>{t(screen.title)}</Text>}
              </View>
            ),
          }}
        />
      ))}
    </Tabs.Navigator>
  );
};

const styles = StyleSheet.create({
  badgeContainer: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: 'red',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    maxHeight: 25,
  },
  iconWrapper: {
    backgroundColor: 'rgba(189, 190, 190, 0.5)',
    borderRadius: 25,
    padding: 10,
  },
  focusedIconWrapper: {
    backgroundColor: 'black',
  },
  iconText: {
    marginLeft: 10,
    fontWeight: '600',
    color: 'white',
  },
});

export default AppScreens;
