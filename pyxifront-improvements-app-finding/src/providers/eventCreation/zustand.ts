import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';
import {TicketType, PromoCode} from '~components/TicketForm';
import {Coords} from '~types/api/user';

interface IEventInfo {
  start_date: string;
  end_date: string;
  description: string;
  address_name: string;
  name: string;
  coords: Coords;
  private: boolean;
  is_paid: boolean;
  number_slots: number;
  isForKids: boolean;
  payment_url: string;
  ticketTypes: TicketType[];
  locationIsChosen: any;
  promoCodes: PromoCode[];
}

type State = {
  subcategory: number[];
  image: string;
  groups: number[];
  eventInfo: IEventInfo;
  setGroups: (value: number[]) => void;
  setEventInfo: (value: IEventInfo) => void;
  setSubCategory: (value: number) => void;
  setImage: (value: string) => void;
  reset: () => void;
};

const initialState = {
  subcategory: [],
  groups: [],
  image: '',
  eventInfo: {
    start_date: '',
    end_date: '',
    description: '',
    address_name: '',
    name: '',
    coords: {
      lat: 0,
      long: 0,
    },
    private: false,
    is_paid: false,
    isForKids: false,
    number_slots: 5,
    payment_url: '',
    ticketTypes: [],
    promoCodes: []
  },
};
export const useEventCreationStore = create<State>()(
  immer((set, get) => ({
    ...initialState,
    setEventInfo: event_info => {
      set({
        eventInfo: event_info,
      });
    },
    setSubCategory: subcategory_id => {
      const currentCategories = get().subcategory || [];

      const newCategories = currentCategories.includes(subcategory_id)
        ? currentCategories.filter(subcategory => subcategory !== subcategory_id)
        : [...currentCategories, subcategory_id];

      set({
        subcategory: newCategories,
      });
    },
    setImage: image => {
      set({
        image,
      });
    },
    setGroups: groups => {
      set({
        groups,
      });
    },
    reset: () => set(initialState),
  })),
);
