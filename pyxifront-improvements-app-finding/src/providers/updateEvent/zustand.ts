import {create} from 'zustand';

export interface SubCategoryType {
  category_id: number | null;
  subcategory_id: number | null;
  subcategory_name: string | null;
  image_url: string | null;
}

type State = {
  subcategory: SubCategoryType[];
  setSubCategory: (subcategory: SubCategoryType) => void;
};

const initialState = {
  subcategory: [],
};

export const useUpdateEventState = create<State>()(set => ({
  ...initialState,
  setSubCategory: subcategory_id => {
    set({
      subcategory: [subcategory_id],
    });
  },
}));
