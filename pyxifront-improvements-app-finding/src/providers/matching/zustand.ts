import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type MatchingType = 'neighbours' | 'anyone' | null;

type State = {
  isVisible: boolean;
  noMatchesButtonIsVisible: boolean;
  close: () => void;
  open: () => void;
  matchingEventId: number | null;
  setMatchingEventId: (value: number | null) => void;
  setNoMatchesButtonIsVisible: (value: boolean) => void;
  domain: string | null;
  setDomain: (value: string | null) => void;
  refresh: boolean; // New refresh flag
  setRefresh: (value: boolean) => void; // New setter for refresh flag
  matchingType: MatchingType;
  setMatchingType: (value: MatchingType) => void;
};

const initialState = {
  isVisible: false,
  matchingEventId: null,
  noMatchesButtonIsVisible: false,
  domain: null,
  refresh: false, // Initial value for the refresh flag
  matchingType: null,
};

export const useMatchingLoaderAnimationStore = create<State>()(
  immer((set, _) => ({
    ...initialState,
    close: () => {
      set({ isVisible: false, noMatchesButtonIsVisible: false });
    },
    open: () => {
      set({ isVisible: true });
    },
    setMatchingEventId: (event_id) => {
      set({ matchingEventId: event_id });
    },
    setNoMatchesButtonIsVisible: (value) => {
      set({ noMatchesButtonIsVisible: value });
    },
    setDomain: (value) => {
      set({ domain: value });
    },
    setRefresh: (value) => { // Setter method for refresh flag
      set({ refresh: value });
    },
    setMatchingType: (value) => {
      set({ matchingType: value });
    },
  })),
);
