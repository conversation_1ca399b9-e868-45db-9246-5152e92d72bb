import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';

type State = {
  subcategories: number[];
  setSubCategory: (value: number) => void;
  setSubCategories: (values: number[]) => void;
  reset: () => void;
};

const initialState = {
  subcategories: [],
};
export const useUpdateUserStore = create<State>()(
  immer((set, get) => ({
    ...initialState,
    setSubCategory: subcategory_id => {
      setTimeout(() => {
        const currentCategories = get().subcategories || [];

        const newCategories = currentCategories.includes(subcategory_id)
          ? currentCategories.filter(subcategory => subcategory !== subcategory_id)
          : [...currentCategories, subcategory_id];

        set({
          subcategories: newCategories,
        });
      });
    },
    setSubCategories: values => {
      set({subcategories: values});
    },
    reset: () => set(initialState),
  })),
);
