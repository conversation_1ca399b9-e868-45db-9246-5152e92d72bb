import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {createJSONStorage, persist} from 'zustand/middleware';
import {boolean} from 'yup';

type State = {
  radius: number;
  isForKids: boolean;
  setRadius: (value: number) => void;
  toggleIsForKids: () => void;
  setIsForKids: (value: boolean) => void;
};

const initialState = {
  radius: 100,
  isForKids: false,
};
export const useHomeStore = create<State>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setRadius: value => {
          set({
            radius: value,
          });
        },
        toggleIsForKids: () => {
          set({
            isForKids: !get().isForKids,
          });
        },
        setIsForKids: (value: boolean) => {
          set({
            isForKids: value,
          });
        },
      }),
      {
        name: 'filters',
        storage: createJSONStorage(() => AsyncStorage),
      },
    ),
  ),
);
