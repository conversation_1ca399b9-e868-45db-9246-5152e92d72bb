import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';
import {Business} from '~types/api/business';
import {User} from '~types/api/user';

type State = {
  user: Omit<User, 'subcategories' | 'groups'> | null;
  business: Business | null;
  setUser: (value: Omit<User, 'subcategories' | 'groups'>) => void;
  setBusiness: (value: Business) => void;
  resetUser: () => void;
};

const initialState = {
  user: null,
  business: null,
};
export const useUserStore = create<State>()(
  immer(set => ({
    ...initialState,
    setUser: user => {
      set({
        user,
      });
    },
    setBusiness: business => {
      set({
        business,
      });
    },
    resetUser: () => set(initialState),
  })),
);
