// store.ts
import {PermissionStatus} from 'react-native';
import {create} from 'zustand';
import {GeoPosition} from 'react-native-geolocation-service';
import {immer} from 'zustand/middleware/immer';

type State = {
  currentPositionState: {
    latitude: number;
    longitude: number;
    address: string;
  } | null;
  permissionStatus: PermissionStatus | null;
  userLocation: (GeoPosition & {locationName: string | null}) | null;
  resetMapsState: () => void;
  setUserLocation: (location: (GeoPosition & {locationName: string | null}) | null) => void;
  setPermissionStatus: (status: PermissionStatus | null) => void;
  setCurrentPositionState: (
    state: {
      latitude: number;
      longitude: number;
      address: string;
    } | null,
  ) => void;
};

export const useMapsContext = create<State>()(
  immer(set => ({
    currentPositionState: null,
    permissionStatus: null,
    userLocation: null,
    setUserLocation: location => set({userLocation: location}),
    setPermissionStatus: status => set({permissionStatus: status}),
    setCurrentPositionState: state => {
      console.log('setCurrentPositionStatesetCurrentPositionState', state);
       
      set({currentPositionState: state})
    },
    resetMapsState: () => set({permissionStatus: null, currentPositionState: null, userLocation: null}),
  })),
);
