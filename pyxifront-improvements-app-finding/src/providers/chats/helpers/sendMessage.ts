import {MutableRefObject} from 'react';
import {WEBSOCKET_MESSAGE_ACTION, WEBSOCKET_TYPE, WebsocketMessageCreateRequest} from '~types/chat';

export const sendMessage =
  (socketRef: MutableRefObject<WebSocket | null>) =>
  ({message, chatId}: {message: string; chatId: number}) => {
    socketRef.current?.send(
      JSON.stringify({
        type: WEBSOCKET_TYPE.MESSAGE,
        message: {
          action: WEBSOCKET_MESSAGE_ACTION.CREATE,
          data: {text: message?.trim(), chat_id: chatId},
        },
      } as WebsocketMessageCreateRequest),
    );
  };
