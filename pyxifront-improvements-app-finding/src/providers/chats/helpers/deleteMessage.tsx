import {MutableRefObject} from 'react';
import {WEBSOCKET_MESSAGE_ACTION, WEBSOCKET_TYPE, WebsocketMessageDeleteRequest} from '~types/chat';

export const deleteMessage =
  (socketRef: MutableRefObject<WebSocket | null>) =>
  ({messageId}: {messageId: string}) => {
    socketRef.current?.send(
      JSON.stringify({
        type: WEBSOCKET_TYPE.MESSAGE,
        message: {
          action: WEBSOCKET_MESSAGE_ACTION.DELETE,
          data: {message_id: messageId},
        },
      } as WebsocketMessageDeleteRequest),
    );
  };
