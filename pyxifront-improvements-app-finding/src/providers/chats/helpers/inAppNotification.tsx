import {ImageSourcePropType} from 'react-native';
import {Easing, Notifier} from 'react-native-notifier';

const DURATION = 15000;
const ANIMATION_TIME = 300;

export const inAppNotification = ({
  title,
  description,
  imageSource,
  onPress,
}: {
  title: string;
  description: string;
  imageSource: ImageSourcePropType | undefined;
  onPress: () => void;
}) => {
  Notifier.showNotification({
    title,
    description,
    duration: DURATION,
    showAnimationDuration: ANIMATION_TIME,
    showEasing: Easing.ease,
    onPress,
    hideOnPress: true,
    componentProps: {
      imageSource,
      titleStyle: {
        color: '#000',
      },
      containerStyle: {
        borderRadius: 16,
      },
    },
  });
};
