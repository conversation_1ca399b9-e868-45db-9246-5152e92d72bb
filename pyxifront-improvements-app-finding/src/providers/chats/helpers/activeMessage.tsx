import {MutableRefObject} from 'react';
import {ACTIVE_CHAT_REQUEST_ACTIONS, WEBSOCKET_TYPE, WebsocketActiveChatRequest} from '~types/chat';

export const activeMessage =
  (socketRef: MutableRefObject<WebSocket | null>) =>
  ({chatId, action}: {chatId: number; action: ACTIVE_CHAT_REQUEST_ACTIONS}) => {
    socketRef.current?.send(
      JSON.stringify({
        type: WEBSOCKET_TYPE.CHAT,
        message: {
          action,
          data: {
            chat_id: chatId,
          },
        },
      } as WebsocketActiveChatRequest),
    );
  };
