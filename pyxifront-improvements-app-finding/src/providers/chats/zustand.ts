import {create} from 'zustand';
import {immer} from 'zustand/middleware/immer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NUMBER_OF_MESSAGES_PER_PAGE} from '~hooks/chats/useGetChatMessages';
import {
  ChatItem,
  ChatState,
  GetChatsResponse,
  Message,
  USER_CHAT_STATUS,
  WebsocketCreateMessageResponse,
} from '~types/chat';

type State = {
  chatsList: ChatItem[];
  chatItem: ChatState | null;
  isAcceptedChats: Record<string, boolean>;
};

type Store = State & {
  paginationUpdateChatsList: (props: GetChatsResponse) => void;
  paginationUpdateChat: (props: ChatState) => void;
  addMessage: (props: Message) => void;
  removeMessage: (props: {messageId: string}) => void;
  clearChatItem: () => void;
  updateChatsListWithNewMessage: (props: WebsocketCreateMessageResponse) => void;
  markAsRead: (props: {chatId: number; userId: string}) => void;
  increaseUnreadCount: (props: {chatId: number; userId: string}) => void;
  acceptChat: (chatId: string) => void;
  checkChatAcceptance: (chatId: string) => Promise<boolean>;
};

const initialState: State = {
  chatItem: null,
  chatsList: [],
  isAcceptedChats: {},
};

export const useChatStore = create<Store>()(
  immer((set, get) => ({
    ...initialState,
    paginationUpdateChat: props => {
      const currentPaginationItem = get().chatItem;

      set({
        chatItem: {
          messages: [...(currentPaginationItem?.messages.slice(0, props.offset) || []), ...props.messages],
          chatId: currentPaginationItem?.chatId,
          offset: props.offset,
        },
      });
    },
    addMessage: props => {
      const currentStateChat = get().chatItem;

      if (currentStateChat?.chatId !== props.chat_id) {
        return;
      }

      set({
        chatItem: {
          messages:
            currentStateChat?.messages &&
            currentStateChat?.messages.length > 0 &&
            currentStateChat?.messages.length % NUMBER_OF_MESSAGES_PER_PAGE === 0
              ? [props, ...(currentStateChat?.messages.slice(0, -1) || [])]
              : [props, ...(currentStateChat?.messages || [])],
          chatId: props.chat_id,
          offset:
            currentStateChat && currentStateChat?.offset > NUMBER_OF_MESSAGES_PER_PAGE
              ? currentStateChat.offset - NUMBER_OF_MESSAGES_PER_PAGE
              : 0,
        },
      });
    },
    removeMessage: props => {
      const currentStateChat = get().chatItem;

      if (!currentStateChat) {
        return;
      }

      set({
        chatItem: {
          messages: currentStateChat?.messages.filter(item => item.message_id !== props.messageId),
          chatId: currentStateChat.chatId,
          offset:
            currentStateChat && currentStateChat?.offset > NUMBER_OF_MESSAGES_PER_PAGE
              ? currentStateChat.offset - NUMBER_OF_MESSAGES_PER_PAGE
              : 0,
        },
      });
    },
    paginationUpdateChatsList: props => {
      const currentChatsList = get().chatsList;

      if (!props.offset) {
        set({
          chatsList: props.items,
        });

        return;
      }

      set({
        chatsList: [...currentChatsList, ...props.items],
      });
    },
    updateChatsListWithNewMessage: ({data}) => {
      const currentChatsList = get().chatsList;

      set({
        chatsList: currentChatsList.map(item =>
          item.chat_id === data.chat_id
            ? {
                ...item,
                last_message: {
                  chat_id: data.chat_id,
                  created_at: data.created_at,
                  message_id: data.message_id,
                  text: data.text,
                  user: {
                    first_name: data.user.first_name,
                    last_name: data.user.last_name,
                    photo: data.user.photo,
                    uid: data.user.uid,
                  },
                },
              }
            : item,
        ),
      });
    },
    markAsRead: ({chatId, userId}: {chatId: number; userId: string}) => {
      const currentChatsList = get().chatsList;

      set({
        chatsList: currentChatsList.map(item =>
          item.chat_id === chatId
            ? {
                ...item,
                users: item.users.map(user => (user.user.uid === userId ? {...user, unread_count: 0} : user)),
              }
            : item,
        ),
      });
    },
    increaseUnreadCount: ({chatId, userId}: {chatId: number; userId: string}) => {
      const currentChatsList = get().chatsList;

      set({
        chatsList: currentChatsList.map(item =>
          item.chat_id === chatId
            ? {
                ...item,
                users: item.users.map(user =>
                  user.user.uid === userId ? {...user, unread_count: user.unread_count + 1} : user,
                ),
              }
            : item,
        ),
      });
    },
    updateChatStatus: ({chatId, userId, status}: {chatId: number; userId: string; status: USER_CHAT_STATUS}) => {
      const currentChatsList = get().chatsList;

      const changeChatStatus = (chat: ChatItem) => {
        return chat.users.map(user => {
          if (user.user.uid === userId) {
            return {...user, status};
          }
          return user;
        });
      };

      set({
        chatsList: currentChatsList.map(item => {
          if (item.chat_id === chatId) {
            return {
              ...item,
              users: changeChatStatus(item),
            };
          }

          return item;
        }),
      });
    },
    clearChatItem: () => {
      set({chatItem: initialState.chatItem});
    },
    acceptChat: async (chatId: string) => {
      set(state => {
        state.isAcceptedChats[chatId] = true;
      });
      await AsyncStorage.setItem(`accepted_${chatId}`, 'true');
    },
    checkChatAcceptance: async (chatId: string) => {
      const value = await AsyncStorage.getItem(`accepted_${chatId}`);
      return value === 'true';
    },
  })),
);
