import AsyncStorage from '@react-native-async-storage/async-storage';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';
import {immer} from 'zustand/middleware/immer';
import {Coords, Gender} from '~types/api/user';

type State = {
  subcategories: number[];
  categories: number[];
  personalOnboarding: {
    photo: string;
    coords: Coords;
    last_name: string;
    gender: Gender;
    first_name: string;
    description: string;
    date_of_birth: string;
    groups: number[];
    children: {child_age: number}[];
  };
  businessOnboarding: {
    name: string;
    description: string;
    coords: Coords;
    business_type_id: number;
    target_audience: string[];
    photo: string;
  };
  onboardingType: ONBOARDING_TYPE_ENUM | null;
  setBusinessOnboardingProfileInfo: (obj: {name: string; description: string; coords: Coords; photo: string}) => void;
  setPersonalOnboardingProfileInfo: (obj: {
    first_name: string;
    last_name: string;
    photo: string;
    coords: Coords;
    gender: Gender;
    description: string;
    date_of_birth: string;
  }) => void;
  setOnboardingType: (value: ONBOARDING_TYPE_ENUM) => void;
  setPersonalGroups: (arr: number[]) => void;
  setPersonalChildren: (arr: string[]) => void;
  setPersonalCategories: (arr: number) => void;
  setPersonalSubCategories: (value: number) => void;
  reset: () => void;
};

enum ONBOARDING_TYPE_ENUM {
  BUSINESS = 'business',
  PERSONAL = 'personal',
}

const initialState = {
  subcategories: [],
  categories: [],
  personalOnboarding: {
    photo: '',
    coords: {lat: 0, long: 0},
    last_name: '',
    gender: Gender.MAN,
    first_name: '',
    description: '',
    date_of_birth: '',
    groups: [],
    children: [],
  },
  businessOnboarding: {
    business_type_id: 1,
    name: '',
    description: '',
    coords: {
      long: 0,
      lat: 0,
    },
    target_audience: [],
    photo: '',
  },
  onboardingType: null,
};
export const useOnboardingStore = create<State>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setBusinessOnboardingProfileInfo: obj =>
          set({
            businessOnboarding: {...get().businessOnboarding, ...obj},
          }),
        setOnboardingType: (value: ONBOARDING_TYPE_ENUM) => set({onboardingType: value}),
        setPersonalOnboardingProfileInfo: obj => set({personalOnboarding: {...get().personalOnboarding, ...obj}}),
        setPersonalGroups: arr => set({personalOnboarding: {...get().personalOnboarding, groups: arr}}),
        setPersonalChildren: (arr: string[]) => {
          const newArr = arr.map(value => {
            const secondValue = value.split('-')[1];
            if (secondValue) {
              return {child_age: +secondValue};
            }
            return {child_age: 18};
          });
          set({personalOnboarding: {...get().personalOnboarding, children: newArr}});
        },
        setPersonalCategories: category_id => {
          setTimeout(() => {
            const currentCategories = get().categories || [];

            const newCategories = currentCategories.includes(category_id)
              ? currentCategories.filter(category => category !== category_id)
              : [...currentCategories, category_id];

            set({
              categories: newCategories,
            });
          });
        },
        setPersonalSubCategories: subcategory_id => {
          setTimeout(() => {
            const currentCategories = get().subcategories || [];

            const newCategories = currentCategories.includes(subcategory_id)
              ? currentCategories.filter(subcategory => subcategory !== subcategory_id)
              : [...currentCategories, subcategory_id];

            set({
              subcategories: newCategories,
            });
          });
        },
        reset: () => set(initialState),
      }),
      {
        name: 'onboarding-storage',
        storage: createJSONStorage(() => AsyncStorage),
      },
    ),
  ),
);
