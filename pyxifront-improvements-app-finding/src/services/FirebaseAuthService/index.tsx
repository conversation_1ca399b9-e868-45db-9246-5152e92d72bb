import appleAuth from '@invertase/react-native-apple-authentication';
import auth, {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {Alert} from 'react-native';
import Config from 'react-native-config';
import OneSignal from 'react-native-onesignal';

class FirebaseAuthService {
  auth = auth();

  constructor() {
    GoogleSignin.configure({
      iosClientId: String(Config.IOS_CLIENT_ID),
      webClientId: String(Config.WEB_CLIENT_ID), // client ID of type WEB for your server (needed to verify user ID and offline access)
      offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
      profileImageSize: 120, // [iOS] The desired height (and width) of the profile image. Defaults to 120px
    });
    this.signInWithGoogle = this.signInWithGoogle.bind(this);
    this.signInWithServices = this.signInWithServices.bind(this);
    this.signInEmailPassword = this.signInEmailPassword.bind(this);
    this.signUp = this.signUp.bind(this);
    this.signInWithApple = this.signInWithApple.bind(this);
    this.sendChangePasswordLink = this.sendChangePasswordLink.bind(this);
    this.logOut = this.logOut.bind(this);
    this.reauthenticateGoogle = this.reauthenticateGoogle.bind(this);
    this.reauthenticateApple = this.reauthenticateApple.bind(this);
    this.reAuthWithCredentials = this.reAuthWithCredentials.bind(this);
    this.reAuthWithEmailAndPassword = this.reAuthWithEmailAndPassword.bind(this);
    this.deleteAccount = this.deleteAccount.bind(this);
  }

  async signInEmailPassword(email: string, password: string, setIsLoading: any) {
    try {
      const response = await this.auth.signInWithEmailAndPassword(email, password);
      console.log(response, 'responseresponse');
      
      if (!response?.user?.emailVerified) {
        response.user.sendEmailVerification();
        setIsLoading(false);
        Alert.alert('Verify your email', "Your email isn't verified, we resend you verification link on your email");
        return;
      }

      console.log('response.user', response.user);
      OneSignal.disablePush(false);
      return {user: response.user};
    } catch (error: any) {
      if (error.code === 'auth/internal-error') {
        setIsLoading(false);
        console.log('The password used was incorrect. Please try again with the correct password.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }

      if (error.code === 'auth/invalid-login') {
        setIsLoading(false);
        console.log('The password used was incorrect. Please try again with the correct password.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }

      if (error.code === 'auth/email-already-in-use') {
        setIsLoading(false);
        console.log('That email address is already in use!');
        Alert.alert('That email address is already in use. Please try to sign in with the correct password.');
      }

      if (error.code === 'auth/invalid-email') {
        setIsLoading(false);
        console.log('That email address is invalid.');
        Alert.alert('That email address is invalid.');
      }

      if (error.code === 'auth/wrong-password') {
        setIsLoading(false);
        console.log('The password used was incorrect. Please try again with the correct password.');
        Alert.alert('The password used was incorrect. Please try again with the correct password.');
      }

      if (error.code === 'auth/user-not-found') {
        setIsLoading(false);
        console.log("This user can't be found. Please try signing up below.");
        Alert.alert("This user can't be found. Please try signing up below.");
      }
      if (error.code === 'auth/network-request-failed') {
        setIsLoading(false);
        console.log('A network error (such as timeout, interrupted connection or unreachable host) has occurred.');
        Alert.alert('A network error (such as timeout, interrupted connection or unreachable host) has occurred.');
      }
      return;
    }
  }

  async signUp(email: string, password: string, confirmPassword: string, setIsLoading: any) {
    try {
      if (confirmPassword !== password) {
        setIsLoading(false);
        Alert.alert("Confirm password doesn't match with password");
        return false;
      }
      const {user} = await this.auth.createUserWithEmailAndPassword(email, password);
      await user.sendEmailVerification();
      setIsLoading(false);
      Alert.alert('A verification link has been sent to your email.', "If you don’t see it, please check your spam folder.");

      await this.logOut();
      return {user};
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        setIsLoading(false);
        Alert.alert('That email address is already in use.');
      }

      if (error.code === 'auth/invalid-email') {
        setIsLoading(false);
        Alert.alert('That email address is invalid.');
      }
      return false;
    }
  }
  
  async signInWithServices(credential: FirebaseAuthTypes.AuthCredential) {
    try {
      const {user} = await this.auth.signInWithCredential(credential);
      OneSignal.disablePush(false);
      return {user};
    } catch (error: any) {
      if (error.code === 'auth/account-exists-with-different-credential') {
        Alert.alert('That email address is already in use with a different account.');
      }

      if (error.code === 'auth/invalid-email') {
        Alert.alert('That email address is invalid.');
      }

      if (error.code === 'auth/user-not-found') {
        Alert.alert("This user can't be found. Please try signing up below.");
      }
      return false;
    }
  }

  async signInWithGoogle() {
    try {
      // Check if your device supports Google Play
      await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});

      // Get the users ID token
      const {idToken} = await GoogleSignin.signIn();

      // Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);

      const authResponse = await this.signInWithServices(googleCredential);

      return authResponse;
    } catch (error: any) {
      console.log(error);
      return false;
    }
  }

  async signInWithApple() {
    try {
      // Start the sign-in request
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      // Ensure Apple returned a user identityToken
      if (!appleAuthRequestResponse.identityToken) {
        Alert.alert('Apple Sign-In failed', 'No identify token returned');
        return false;
      }

      // Create a Firebase credential from the response
      const {identityToken, nonce} = appleAuthRequestResponse;
      const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

      const authResponse = await this.signInWithServices(appleCredential);

      return authResponse;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async sendChangePasswordLink(email: string) {
    try {
      await this.auth.sendPasswordResetEmail(email);
      Alert.alert(
        'Repair link was successfully sent',
        "check your email. If you couldn't see it, it's probably on the spam",
      );
      return true;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async logOut() {
    try {
      await this.auth.signOut();
      return true;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async reauthenticateGoogle() {
    try {
      // Get the current user
      const user = this.auth.currentUser;
      // Get the account
      const acct = await GoogleSignin.signInSilently();
      if (acct) {
        const credential = auth.GoogleAuthProvider.credential(acct.idToken);
        await user?.reauthenticateWithCredential(credential);

        return true;
      }
      return false;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async reauthenticateApple() {
    try {
      const response = await this.signInWithApple();
      if (response) {
        return true;
      }
      Alert.alert('Something went wrong', 'Try re-login and make it again or Ask Pyxi for help');
      return false;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async reAuthWithCredentials() {
    try {
      const user = this.auth.currentUser;
      const providerData = user?.providerData[0].providerId;
      if (providerData === 'google.com') {
        await this.reauthenticateGoogle();
        return 1;
      }

      if (providerData === 'apple.com') {
        await this.reauthenticateApple();
        return 1;
      }
      return 0;
    } catch (error) {
      return -1;
    }
  }

  async reAuthWithEmailAndPassword(password: string) {
    try {
      const user = this.auth.currentUser!;
      const credential = auth.EmailAuthProvider.credential(user.email!, password);
      const reauthResponse = await user?.reauthenticateWithCredential(credential);

      return !!reauthResponse;
    } catch (error) {
      return false;
    }
  }

  async deleteAccount() {
    try {
      await this.auth.currentUser!.delete();
      return;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
    }
  }

  async getAuthToken() {
    try {
      const token = await this.auth.currentUser?.getIdToken();
      return token || '';
    } catch (error) {
      return '';
    }
  }
}

const FirebaseAuth = new FirebaseAuthService();

export default FirebaseAuth;
