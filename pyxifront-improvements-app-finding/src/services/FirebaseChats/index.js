'use strict';
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : {default: mod};
  };
Object.defineProperty(exports, '__esModule', {value: true});
const firestore_1 = __importDefault(require('@react-native-firebase/firestore'));
const auth_1 = __importDefault(require('@react-native-firebase/auth'));
// import messaging from '@react-native-firebase/messaging';
// import { Alert } from 'react-native'; // Імпортуємо Alert для відображення сповіщень в додатку
const chat_1 = require('../../types/chat');
const moment_1 = __importDefault(require('moment'));
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import {event} from 'firebase-functions/v1/analytics';
import i18n from 'i18next';
import "react-native-get-random-values";
import { v4 as uuidv4 } from 'uuid';
// import OneSignalService from '~services/FirebaseNotificationsService';
// import {Notifier, NotifierComponents} from 'react-native-notifier';
class FirebaseChats {
  deleteGroupChat(arg0) {
    throw new Error('Method not implemented.');
  }
  constructor() {
    this.firestore = (0, firestore_1.default)();
    this.createNewGroupChat = this.createNewGroupChat.bind(this);
    this.createPrivateChat = this.createPrivateChat.bind(this);
    this.createPyxiConciergeChat = this.createPyxiConciergeChat.bind(this);
    this.removeUserFromTheGroupChat = this.removeUserFromTheGroupChat.bind(this);
    this.pushConciergeMessage = this.pushConciergeMessage.bind(this);
    this.pushMessageForUsers = this.pushMessageForUsers.bind(this);
    this.updateUserFcmToken = this.updateUserFcmToken.bind(this);
    this.createOrganisationChat = this.createOrganisationChat.bind(this);
    this.createContactUsChat = this.createContactUsChat.bind(this);
    // this.handleForegroundNotification = this.handleForegroundNotification.bind(this);
    // Викликаємо метод для обробки сповіщень на передньому плані
    // this.handleForegroundNotification();
  }
  async updateUserFcmToken(token) {
    try {
      const currentUser = (0, auth_1.default)().currentUser;
      if (currentUser) {
        const userDocRef = this.firestore.collection('users').doc(currentUser.uid);
        await userDocRef.set(
          {
            deviceFcmToken: token,
          },
          {merge: true},
        );
      } else {
        console.log('No user is signed in.');
      }
    } catch (error) {
      console.error('Error updating FCM token in Firestore:', error);
    }
  }
  async sendPushNotification(token, title, message, data) {
    const serverKey =
      'AAAA3g8xYT0:APA91bEs7-It_xwurSWKqQ0pCUQIhR19CS0nEEIpv3j04zKczi6MTuwE7Ed4NAN28EyliGWppc3KFiymdM1fTSpKMQF_kBSjGb6patTJqkC_1Z3cbRTM42mm_9egAiO87hexW683ONVT';
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `key=${serverKey}`,
        },
        body: JSON.stringify({
          to: token,
          notification: {
            title: title,
            body: message,
          },
          data: Object.assign({}, data),
        }),
      });
      const responseData = await response.json();
      console.log('FCM response:', responseData);
    } catch (error) {
      console.error('Error sending FCM message:', error);
    }
  }
  // async handleForegroundNotification() {
  //   messaging().onMessage(async remoteMessage => {
  //     console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
  //     // Відображаємо сповіщення в додатку за допомогою Alert
  //     Notifier.showNotification({
  //       title: remoteMessage?.notification?.title,
  //       description: remoteMessage?.notification?.body,
  //       Component: NotifierComponents.Alert,
  //       componentProps: {
  //         alertType: 'info',
  //       },
  //     });
  //   });
  // }
  async createNewGroupChat({event_id, event_image, event_name, user_name, user_id, otherUsersId, otherUsersName}) {
    var _a, _b;
    try {
      const existingChat = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
      if ((_a = existingChat.docs[0]) === null || _a === void 0 ? void 0 : _a.id) {
        return existingChat.docs[0].id;
      }
      await this.firestore.collection('chats').add({
        type: chat_1.CHAT_TYPE_ENUM.GROUP,
        userIds: [user_id, ...otherUsersId],
        users: [user_name, ...otherUsersName],
        history: [
          {
            message_id: uuidv4(),
            message: `Welcome to ${event_name}!`,
            type: chat_1.CHAT_MESSAGE_TYPE_ENUM.INFO,
            sender_image: event_image,
            sender_id: '',
            sender: '',
            readUserIds: [],
            timestamp: (0, moment_1.default)().toISOString(),
          },
        ],
        eventImage: event_image,
        eventName: event_name,
        eventId: event_id,
      });
      const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
      if ((_b = querySnapshot.docs[0]) === null || _b === void 0 ? void 0 : _b.id) {
        return querySnapshot.docs[0].id;
      }
    } catch (e) {
      console.error(e);
    }
    return undefined; // Додано повернення undefined у випадку помилки або відсутності значення
  }
  async addNewUserToTheGroupChat({user_id, event_id, user_name, user_image}) {
    try {
      const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
      querySnapshot.forEach(async snap => {
        const currentChat = snap.data();
        await snap.ref.update({
          history: [
            ...currentChat.history,
            {
              message_id: uuidv4(),
              message: `${user_name} has joined the event`,
              type: chat_1.CHAT_MESSAGE_TYPE_ENUM.INFO,
              sender_image: user_image,
              sender_id: '',
              sender: user_name,
              readUserIds: [],
              timestamp: (0, moment_1.default)().toISOString(),
            },
          ],
          users: [...currentChat.users, user_name],
          userIds: [...currentChat.userIds, user_id],
        });
      });
    } catch (_) {}
  }
  async createContactUsChat({user_id1, user_id2, user_name1, user_name2}) {
    try {
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();

      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data();
        return chat.type === 'contact-pyxi' && chat.userIds.includes(user_id2);
      });

      if (existChatSnap) {
        return existChatSnap.id;
      } else {
        const newChatData = {
          type: 'contact-pyxi',
          userIds: [user_id1, user_id2],
          users: [user_name1, user_name2],
          progress: '',
          history: [],
        };

        const newChat = await this.firestore.collection('chats').add(newChatData);
        return newChat.id;
      }
    } catch (_) {
      console.log(_, 'errorerrorerrorerrorerror');

      return '';
    }
  }

  async createPrivateChat({user_id1, user_id2, user_name1, user_name2, user_image, event}) {
    try {
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();
      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data();
        return chat.type === chat_1.CHAT_TYPE_ENUM.PRIVATE && chat.userIds.includes(user_id2);
      });
      if (existChatSnap) {
        const chatRef = this.firestore.collection('chats').doc(existChatSnap.id);
        const dataRef = (await chatRef.get()).data();
        if (event) {
          const updated = [
            ...dataRef.history,
            {
              message_id: uuidv4(),
              message: ``,
              type: chat_1.CHAT_MESSAGE_TYPE_ENUM.EVENT,
              sender_image: user_image,
              sender_id: user_id1,
              sender: user_name1,
              readUserIds: [],
              timestamp: (0, moment_1.default)().toISOString(),
              event: event,
            },
          ];

          await chatRef.update({
            history: updated,
          });
        }
        return existChatSnap.id;
      } else {
        const history = [
          {
            message_id: uuidv4(),
            message: `Hi ${user_name2}`,
            type: chat_1.CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
            sender_image: user_image,
            sender_id: user_id1,
            sender: user_name1,
            readUserIds: [],
            timestamp: (0, moment_1.default)().toISOString(),
          },
        ];
        if (event) {
          history.push({
            message_id: uuidv4(),
            message: ``,
            type: chat_1.CHAT_MESSAGE_TYPE_ENUM.EVENT,
            sender_image: user_image,
            sender_id: user_id1,
            sender: user_name1,
            readUserIds: [],
            timestamp: (0, moment_1.default)().toISOString(),
            event: event,
          });
        }
        const newChatData = {
          type: chat_1.CHAT_TYPE_ENUM.PRIVATE,
          userIds: [user_id1, user_id2],
          users: [user_name1, user_name2],
          status: 'open',
          progress: 'new',
          history: history,
          event: event,
        };
        const newChat = await this.firestore.collection('chats').add(newChatData);
        return newChat.id;
      }
    } catch (_) {
      return '';
    }
  }
  async createOrganisationChat({user_id1, user_id2, user_name1, user_name2, user_image, isTechnical, event}) {
    try {
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();
      const chatsSnapshot = await this.firestore.collection('chats').get();
      const totalChats = chatsSnapshot.size;

      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data();
        return chat.type === chat_1.CHAT_TYPE_ENUM.ORGANISATION && chat.userIds.includes(user_id2);
      });

      if (existChatSnap) {
        const chatRef = this.firestore.collection('chats').doc(existChatSnap.id);
        chatRef.update({status: 'open', event: event});
        if (event) {
          console.log(event, 'eventeventevent');
          
          const dataRef = (await chatRef.get()).data();

          const updated = [
            ...dataRef.history,
            {
              message_id: uuidv4(),
              message: ``,
              type: chat_1.CHAT_MESSAGE_TYPE_ENUM.EVENT,
              sender_image: user_image,
              sender_id: user_id1,
              sender: user_name1,
              readUserIds: [],
              timestamp: (0, moment_1.default)().toISOString(),
              event: event,
            },
          ];

          await chatRef.update({
            history: updated,
          });
        }
        return existChatSnap.id;
      } else {
        if (isTechnical) {
          const newChatData = {
            type: chat_1.CHAT_TYPE_ENUM.ORGANISATION,
            userIds: [user_id1, user_id2],
            users: [user_name1, user_name2],
            issueNumber: [totalChats],
            status: 'open',
            event: event,
            progress: 'new',
            history: [
              {
                message_id: uuidv4(),
                message: i18n.t('technical_issue', {name: 'Pyxi'}),
                type: chat_1.CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
                sender_image: user_image,
                sender_id: user_id1,
                sender: user_name1,
                readUserIds: [],
                timestamp: (0, moment_1.default)().toISOString(),
              },
            ],
          };
          const newChat = await this.firestore.collection('chats').add(newChatData);
          return newChat.id;
        } else {
          const history = [
            {
              message_id: uuidv4(),
              message: i18n.t('greeting', {user_name1: user_name2}),
              type: chat_1.CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
              sender_image: user_image,
              sender_id: user_id1,
              sender: user_name1,
              readUserIds: [],
              timestamp: (0, moment_1.default)().toISOString(),
            },
          ];
          if (event) {
            history.push({
              message_id: uuidv4(),
              message: ``,
              type: chat_1.CHAT_MESSAGE_TYPE_ENUM.EVENT,
              sender_image: user_image,
              sender_id: user_id1,
              sender: user_name1,
              readUserIds: [],
              timestamp: (0, moment_1.default)().toISOString(),
              event: event,
            });
          }
          const newChatData = {
            type: chat_1.CHAT_TYPE_ENUM.ORGANISATION,
            userIds: [user_id1, user_id2],
            users: [user_name1, user_name2],
            issueNumber: [totalChats],
            status: 'open',
            event: event,
            progress: 'new',
            history: history,
          };
          const newChat = await this.firestore.collection('chats').add(newChatData);
          return newChat.id;
        }
      }
    } catch (_) {
      console.log(_);

      return '';
    }
  }
  async createPyxiConciergeChat({user_id, user_name, user_image}) {
    const conciergeChatExists = await this.firestore.collection('conciergeChat').where('userId', '==', user_id).get();
    conciergeChatExists.empty &&
      (await this.firestore.collection('conciergeChat').add({
        type: chat_1.CONCIERGE_CHAT_TYPE_ENUM.ONE_ON_ONE,
        userId: user_id,
        userName: user_name,
        userImage: user_image,
        messages: [
          {
            timestamp: (0, moment_1.default)().toISOString(),
            senderId: user_id,
            sender: user_name,
            status: chat_1.BUTTON_STATUS_ENUM.EMPTY,
            readUserIds: [],
            message:
              'Welcome to Pyxi! Through this chat, you can effortlessly reach our Concierge, keep track of upcoming events, and stay updated with the latest news. How may we assist you today?',
          },
        ],
      }));
  }
  async removeUserFromTheGroupChat({user_id, event_id, user_name, event_image}) {
    const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
    if (querySnapshot.empty) {
      return;
    }
    querySnapshot.forEach(async snap => {
      const currentChat = snap.data();
      await snap.ref.update({
        userIds: [...currentChat.userIds.filter(userId => userId !== user_id)],
        history: [
          ...currentChat.history,
          {
            message_id: uuidv4(),
            message: `${user_name} has exited the chat`,
            type: chat_1.CHAT_MESSAGE_TYPE_ENUM.INFO,
            sender_image: event_image,
            sender_id: '',
            sender: user_name,
            readUserIds: [...currentChat.userIds.filter(userId => userId !== user_id)],
            timestamp: (0, moment_1.default)().toISOString(),
          },
        ],
      });
    });
  }
  async callEmailNotificationApi(message_id, chat_id) {
    try {
      const token = await FirebaseAuth.getAuthToken();
      const url = `${Config.BASE_API_URL}/firestore/chats/unread-users/${chat_id}/${message_id}`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      const responseData = await response.json();
      console.log('callEmailNotificationApi', responseData);
    } catch (error) {
      console.log('callEmailNotificationApi', error);
    }
  }
  async pushMessageForUsers({chat_id, user_id, user_name, text, user_image}) {
    try {
      const message_id = uuidv4();
      const datetime = (0, moment_1.default)().toISOString();
      const chatRef = this.firestore.collection('chats').doc(chat_id);
      const dataRef = (await chatRef.get()).data();
      const updated = [
        ...dataRef.history,
        {
          message_id: message_id,
          message: text,
          sender: user_name,
          sender_image: user_image,
          sender_id: user_id,
          timestamp: datetime,
          type: chat_1.CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
          readUserIds: [user_id],
        },
      ];
      this.callEmailNotificationApi(message_id, chat_id)
      await chatRef.update({
        history: updated,
      });
      const userUidsArr = [...dataRef.userIds.filter(id => id !== user_id)];
      if (userUidsArr.length > 0) {
        const requestNotificationBody = {
          headings: {
            en: user_name,
          },
          contents: {
            en: text,
          },
          data: {
            chat_id: chat_id,
            type: 'ChatNotification',
            chat_type: dataRef.type,
            clickAction: 'OPEN_CHAT',
          },
          include_external_user_ids: userUidsArr,
        };

        const token = await FirebaseAuth.getAuthToken();
        const url = `${Config.BASE_API_URL}/send-notification`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: token,
          },
          body: JSON.stringify(requestNotificationBody),
        });

        const responseData = await response.json();
        console.log('new Message', responseData);
      }
    } catch (error) {
      console.log(error);
    }
  }
  async pushConciergeMessage({chat_id, user_id, user_name, text}) {
    const datetime = (0, moment_1.default)().toISOString();
    const chatRef = this.firestore.collection('conciergeChat').doc(chat_id);
    const dataRef = (await chatRef.get()).data();
    const updated = [
      ...(dataRef === null || dataRef === void 0 ? void 0 : dataRef.messages),
      {
        senderId: user_id,
        message: text,
        sender: user_name,
        timestamp: datetime,
        readUserIds: [user_id],
      },
    ];
    await chatRef.update({
      messages: updated,
    });
    // let data = {
    //   uid: user_id,
    //   title: user_name,
    //   message: text,
    //   additional_data: {
    //     chat_id: chat_id,
    //     type: 'AdminChat',
    //   },
    // };
    try {
      // await OneSignalService.sendPushNotification(data);
    } catch (error) {
      console.log(error);
    }
    return;
  }
}
const FirebaseChatsService = new FirebaseChats();
exports.default = FirebaseChatsService;
//# sourceMappingURL=index.js.map
