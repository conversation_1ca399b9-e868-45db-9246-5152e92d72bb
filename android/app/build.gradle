buildscript {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:[0.12.10, 0.99.99]'
        classpath 'com.android.tools.build:gradle:7.0.4' // Додайте це, якщо його немає
        classpath 'com.google.gms:google-services:4.3.10' // Додайте це, якщо його немає
    }
}

apply plugin: 'com.onesignal.androidsdk.onesignal-gradle-plugin'
apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.facebook.react'
apply plugin: 'com.google.firebase.crashlytics'
apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesAppBuildGradle(project)

apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

android {
    ndkVersion rootProject.ext.ndkVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.pyxida.pyxida"
    defaultConfig {
        manifestPlaceholders = [GOOGLE_API_KEY: project.env.get("GOOGLE_API_KEY")]
        applicationId "com.pyxida.pyxida"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 149
        versionName "12.4.31"
    }
    flavorDimensions "react-native-camera" // Додайте цю строку
    productFlavors {
        general {
            dimension "react-native-camera"
        }
        mlkit {
            dimension "react-native-camera"
        }
    }
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug // If you have a separate key for debug, otherwise you can remove this line
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled false // Вимкніть мінімізацію, якщо у вас немає proguard-rules.pro або налагоджувальних правил
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

repositories {
    google()
    mavenCentral()
}

dependencies {
    implementation project(':react-native-camera')
    implementation("com.facebook.react:react-android")
    implementation 'com.google.firebase:firebase-dynamic-links:21.0.0'
    implementation project(':react-native-splash-screen')
    implementation project(':react-native-version-check')
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    // debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}")
    // debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
    //     exclude group:'com.squareup.okhttp3', module:'okhttp'
    // }
    // debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")
    // if (hermesEnabled.toBoolean()) {
    //     implementation("com.facebook.react:hermes-android")
    // } else {
    //     implementation jscFlavor
    // }
}
