<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:textColor">#000000</item>

         <!-- Status Bar Customization -->
        <item name="android:statusBarColor">#FCF9ED</item> <!-- Change this to your desired color -->
        <item name="android:windowLightStatusBar">true</item> <!-- Makes icons dark for light backgrounds -->
    </style>
    <style  name="SplashScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:statusBarColor">#FCF9ED</item>
    </style>
</resources>
