buildscript {
    ext {
        buildToolsVersion = "33.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        kotlinVersion = "1.8.20"
        playServicesLocationVersion = "21.0.1"
        ndkVersion = "23.1.7779620"
        gradleVersion = "7.4.2"
        reactNativeGradlePluginVersion = "0.71.4"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:$gradleVersion")
        classpath("com.facebook.react:react-native-gradle-plugin:$reactNativeGradlePluginVersion")
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.7'
    }
}

allprojects {
    repositories {
        mavenLocal()
        mavenCentral()
        google()
    }
}
