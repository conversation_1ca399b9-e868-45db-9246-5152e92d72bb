import {StyleSheet, Dimensions, Platform} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
import {spacing, typography, borderRadius, shadows} from '~constants/design';

const {width} = Dimensions.get('window');

// Create styles using the theme
// Create styles using the theme
export const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingTop: spacing.lg,
    },
    header: {
      fontFamily: 'BauhausBuglerW00-Medium',
      fontSize: 32,
      fontWeight: typography.fontWeight.bold,
      color: '#1D1E20',
      marginBottom: spacing.xs,
      textAlign: 'center',
    },
    subheader: {
      fontSize: 16,
      fontWeight: typography.fontWeight.normal,
      color: '#1D1E20',
      marginBottom: spacing.xl,
      textAlign: 'center',
    },
    optionCard: {
      backgroundColor: colors.white,
      borderRadius: 11,
      padding: 14,
      marginBottom: spacing['2xl'],
      borderWidth: 1,
      borderColor: colors.border + '20',
      ...shadows.sm,
    },
    paidOptionCard: {
      backgroundColor: '#F5A865',
      borderWidth: 0,
    },
    optionTitle: {
      fontSize: 28,
      fontWeight: 'bold',
      color:  '#FFFFFF',
      marginBottom: spacing.sm,
    },
    optionDescription: {
      fontSize: 18,
      fontWeight: '600',
      color:  '#FFFFFF',
      marginBottom: 8
    },
    optionDescriptionA: {
      fontSize: 14,
      opacity: 0.8,
      fontWeight: typography.fontWeight.normal,
      color:  '#FFFFFF',
      marginBottom: 8
    },
    priceContainer: {
      backgroundColor: colors.white,
      borderRadius: 32,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing['4xl'],
      alignSelf: 'center',
      marginTop: spacing.md,
      width: '100%',
      alignItems: 'center'
    },
    freePriceContainer: {
      backgroundColor: '#F5A865',
    },
    priceText: {
      fontSize: 16,
      fontWeight: typography.fontWeight.bold,
      color: '#1D1E20',
    },
    freePriceText: {
      color: colors.white,
    },
    // Add header row for logo and back button
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
      marginTop: spacing.md,
      marginHorizontal: spacing.lg,
    },
    backButton: {
      width: 44,
      height: 44,
      borderRadius: 12,
    },
    logoContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

if (Platform.OS === 'ios') {
  console.log('Available fonts:', JSON.stringify(Object.keys(require('react-native').StyleSheet.flatten({fontFamily: ''})), null, 2));
}

