import {BASE_API_URL} from '@env';
import {useMutation} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {PaymentIntentResponse} from '~types/api/event';

export interface CreatePayPerMatchPaymentIntentPayload {
  eventId: number;
}

export function useCreatePayPerMatchPaymentIntent() {
  return useMutation<PaymentIntentResponse, Error, CreatePayPerMatchPaymentIntentPayload>(
    async (payloadBody: CreatePayPerMatchPaymentIntentPayload) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${BASE_API_URL}events/${payloadBody.eventId}/pay-per-match/create-payment-intent`, {
        method: 'POST',
        body: '',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });


      if (!response.ok) {
        const responseData = await response.json();
        throw new Error(responseData.detail || 'Failed to create pay-per-match payment intent');
      }

      const data = await response.json();

      return data;
    },
  );
}
