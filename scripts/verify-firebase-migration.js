#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Directories to check
const DIRECTORIES = ['src'];

// Patterns to check for
const CHECKS = {
  modularImports: {
    pattern: /import\s*{\s*(getAuth|getFirestore|getStorage|getDatabase|getFunctions|getMessaging)\s*}\s*from\s*['"]@react-native-firebase/g,
    description: 'Modular Firebase imports (should be 0)',
    shouldBe: 0
  },
  legacyImports: {
    pattern: /import\s+(auth|firestore|storage|database|functions|messaging)\s+from\s+['"]@react-native-firebase\//g,
    description: 'Legacy Firebase imports',
    shouldBe: 'many'
  },
  modularUsage: {
    pattern: /const\s+(auth|firestore|storage|database|functions|messaging)\s*=\s*get(Auth|Firestore|Storage|Database|Functions|Messaging)\(\)/g,
    description: 'Modular Firebase service instantiation (should be 0)',
    shouldBe: 0
  },
  exclamationUsage: {
    pattern: /(auth|firestore|storage|database|functions|messaging)!\.(currentUser|collection|ref|httpsCallable|getToken)/g,
    description: 'Firebase services with exclamation marks (should be 0)',
    shouldBe: 0
  }
};

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function checkFile(filePath, pattern) {
  if (!fs.existsSync(filePath)) {
    return [];
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const matches = [];
  let match;

  while ((match = pattern.exec(content)) !== null) {
    const lines = content.substring(0, match.index).split('\n');
    const lineNumber = lines.length;
    matches.push({
      file: filePath,
      line: lineNumber,
      match: match[0],
      context: lines[lines.length - 1].trim()
    });
  }

  return matches;
}

function runChecks() {
  console.log('🔍 Verifying Firebase Migration...\n');

  const allFiles = [];
  DIRECTORIES.forEach(dir => {
    if (fs.existsSync(dir)) {
      allFiles.push(...getAllFiles(dir));
    }
  });

  console.log(`📁 Checking ${allFiles.length} files...\n`);

  let allPassed = true;

  Object.entries(CHECKS).forEach(([checkName, check]) => {
    console.log(`🔎 ${check.description}:`);
    
    const allMatches = [];
    allFiles.forEach(filePath => {
      // Reset regex lastIndex for global patterns
      check.pattern.lastIndex = 0;
      const matches = checkFile(filePath, check.pattern);
      allMatches.push(...matches);
    });

    if (check.shouldBe === 0) {
      if (allMatches.length === 0) {
        console.log(`   ✅ PASS - Found ${allMatches.length} matches`);
      } else {
        console.log(`   ❌ FAIL - Found ${allMatches.length} matches (expected 0)`);
        allMatches.slice(0, 5).forEach(match => {
          console.log(`      - ${match.file}:${match.line} - ${match.match}`);
        });
        if (allMatches.length > 5) {
          console.log(`      ... and ${allMatches.length - 5} more`);
        }
        allPassed = false;
      }
    } else {
      console.log(`   ℹ️  INFO - Found ${allMatches.length} matches`);
      if (allMatches.length > 0) {
        console.log(`      Sample: ${allMatches[0].file}:${allMatches[0].line} - ${allMatches[0].match}`);
      }
    }
    console.log('');
  });

  // Additional checks
  console.log('🔎 Additional verification checks:');
  
  // Check for any remaining getAuth(), getFirestore() calls
  const directCallPattern = /\b(getAuth|getFirestore|getStorage|getDatabase|getFunctions|getMessaging)\(\)/g;
  const directCallMatches = [];
  allFiles.forEach(filePath => {
    directCallPattern.lastIndex = 0;
    const matches = checkFile(filePath, directCallPattern);
    // Filter out legitimate method calls like FirebaseAuth.getAuthToken()
    const filteredMatches = matches.filter(match => 
      !match.context.includes('.getAuthToken') && 
      !match.context.includes('getAuthToken()') &&
      !match.context.includes('async getAuth')
    );
    directCallMatches.push(...filteredMatches);
  });

  if (directCallMatches.length === 0) {
    console.log('   ✅ PASS - No remaining modular Firebase function calls');
  } else {
    console.log(`   ❌ FAIL - Found ${directCallMatches.length} remaining modular Firebase function calls`);
    directCallMatches.slice(0, 3).forEach(match => {
      console.log(`      - ${match.file}:${match.line} - ${match.match}`);
    });
    allPassed = false;
  }

  console.log('\n' + '='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 MIGRATION VERIFICATION PASSED!');
    console.log('✅ All Firebase code has been successfully migrated to legacy API');
    console.log('✅ No remaining modular Firebase patterns found');
    console.log('✅ Ready to use with older Firebase API patterns');
  } else {
    console.log('❌ MIGRATION VERIFICATION FAILED!');
    console.log('⚠️  Some issues were found that need to be addressed');
  }
  
  console.log('='.repeat(60));
}

// Run the verification
runChecks();
