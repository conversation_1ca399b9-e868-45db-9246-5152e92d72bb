#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Directories to process
const DIRECTORIES = ['src'];

// Firebase service mappings for reverting from modular to legacy API
const FIREBASE_REVERSIONS = [
  // Auth reversions
  {
    modularImport: /import\s*{\s*getAuth\s*}\s*from\s*['"]@react-native-firebase\/auth['"];?\s*\n?/g,
    legacyImport: "import auth from '@react-native-firebase/auth';\n",
    replacements: [
      { from: /const\s+auth\s*=\s*getAuth\(\);?\s*\n?/g, to: '' },
      { from: /getAuth\(\)/g, to: 'auth()' }
    ]
  },
  // Mixed auth imports (auth, {getAuth})
  {
    modularImport: /import\s+auth\s*,\s*{\s*getAuth\s*}\s*from\s*['"]@react-native-firebase\/auth['"];?\s*\n?/g,
    legacyImport: "import auth from '@react-native-firebase/auth';\n",
    replacements: [
      { from: /const\s+auth\s*=\s*getAuth\(\);?\s*\n?/g, to: '' },
      { from: /getAuth\(\)/g, to: 'auth()' }
    ]
  },
  // Firestore reversions
  {
    modularImport: /import\s*{\s*getFirestore\s*}\s*from\s*['"]@react-native-firebase\/firestore['"];?\s*\n?/g,
    legacyImport: "import firestore from '@react-native-firebase/firestore';\n",
    replacements: [
      { from: /const\s+firestore\s*=\s*getFirestore\(\);?\s*\n?/g, to: '' },
      { from: /getFirestore\(\)/g, to: 'firestore()' }
    ]
  },
  // Mixed firestore imports
  {
    modularImport: /import\s+firestore\s*,\s*{\s*getFirestore\s*}\s*from\s*['"]@react-native-firebase\/firestore['"];?\s*\n?/g,
    legacyImport: "import firestore from '@react-native-firebase/firestore';\n",
    replacements: [
      { from: /const\s+firestore\s*=\s*getFirestore\(\);?\s*\n?/g, to: '' },
      { from: /getFirestore\(\)/g, to: 'firestore()' }
    ]
  },
  // Storage reversions
  {
    modularImport: /import\s*{\s*getStorage\s*}\s*from\s*['"]@react-native-firebase\/storage['"];?\s*\n?/g,
    legacyImport: "import storage from '@react-native-firebase/storage';\n",
    replacements: [
      { from: /const\s+storage\s*=\s*getStorage\(\);?\s*\n?/g, to: '' },
      { from: /getStorage\(\)/g, to: 'storage()' }
    ]
  },
  // Mixed storage imports
  {
    modularImport: /import\s+storage\s*,\s*{\s*getStorage\s*}\s*from\s*['"]@react-native-firebase\/storage['"];?\s*\n?/g,
    legacyImport: "import storage from '@react-native-firebase/storage';\n",
    replacements: [
      { from: /const\s+storage\s*=\s*getStorage\(\);?\s*\n?/g, to: '' },
      { from: /getStorage\(\)/g, to: 'storage()' }
    ]
  },
  // Database reversions
  {
    modularImport: /import\s*{\s*getDatabase\s*}\s*from\s*['"]@react-native-firebase\/database['"];?\s*\n?/g,
    legacyImport: "import database from '@react-native-firebase/database';\n",
    replacements: [
      { from: /const\s+database\s*=\s*getDatabase\(\);?\s*\n?/g, to: '' },
      { from: /getDatabase\(\)/g, to: 'database()' }
    ]
  },
  // Functions reversions
  {
    modularImport: /import\s*{\s*getFunctions\s*}\s*from\s*['"]@react-native-firebase\/functions['"];?\s*\n?/g,
    legacyImport: "import functions from '@react-native-firebase/functions';\n",
    replacements: [
      { from: /const\s+functions\s*=\s*getFunctions\(\);?\s*\n?/g, to: '' },
      { from: /getFunctions\(\)/g, to: 'functions()' }
    ]
  },
  // Messaging reversions
  {
    modularImport: /import\s*{\s*getMessaging\s*}\s*from\s*['"]@react-native-firebase\/messaging['"];?\s*\n?/g,
    legacyImport: "import messaging from '@react-native-firebase/messaging';\n",
    replacements: [
      { from: /const\s+messaging\s*=\s*getMessaging\(\);?\s*\n?/g, to: '' },
      { from: /getMessaging\(\)/g, to: 'messaging()' }
    ]
  }
];

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { processed: false, changes: [] };
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const changes = [];

  FIREBASE_REVERSIONS.forEach(reversion => {
    // Check if file has the modular import
    if (content.match(reversion.modularImport)) {
      // Replace modular import with legacy import
      content = content.replace(reversion.modularImport, reversion.legacyImport);
      hasChanges = true;
      changes.push(`Reverted to legacy import`);

      // Apply replacements
      reversion.replacements.forEach(replacement => {
        if (content.match(replacement.from)) {
          content = content.replace(replacement.from, replacement.to);
          changes.push(`Applied replacement: ${replacement.from} -> ${replacement.to}`);
        }
      });
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
  }

  return { processed: hasChanges, changes };
}

function processDirectory(dirPath) {
  const files = getAllFiles(dirPath);
  let totalProcessed = 0;

  files.forEach(filePath => {
    const result = processFile(filePath);
    if (result.processed) {
      console.log(`✅ ${filePath}`);
      result.changes.forEach(change => {
        console.log(`   - ${change}`);
      });
      totalProcessed++;
    }
  });

  return totalProcessed;
}

// Main execution
console.log('🔄 Starting Firebase API reversion from modular to legacy...\n');
console.log('This will revert from modular API to namespaced API:\n');
console.log('  getAuth() → auth()');
console.log('  getFirestore() → firestore()');
console.log('  getStorage() → storage()');
console.log('  getDatabase() → database()');
console.log('  getFunctions() → functions()');
console.log('  getMessaging() → messaging()\n');

let totalProcessed = 0;
for (const dir of DIRECTORIES) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    totalProcessed += processDirectory(dir);
  }
}

console.log(`\n🎉 Firebase API reversion complete!`);
console.log(`📊 Total files processed: ${totalProcessed}`);
