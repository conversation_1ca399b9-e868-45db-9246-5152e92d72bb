#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Directories to process
const DIRECTORIES = ['src'];

// Firebase fixes for remaining issues
const FIREBASE_FIXES = [
  // Fix auth!.currentUser -> auth().currentUser (with exclamation mark)
  {
    pattern: /\bauth!\.currentUser/g,
    replacement: 'auth().currentUser'
  },
  // Fix firestore!.collection -> firestore().collection (with exclamation mark)
  {
    pattern: /\bfirestore!\.collection/g,
    replacement: 'firestore().collection'
  },
  // Fix storage!.ref -> storage().ref (with exclamation mark)
  {
    pattern: /\bstorage!\.ref/g,
    replacement: 'storage().ref'
  },
  // Fix database!.ref -> database().ref (with exclamation mark)
  {
    pattern: /\bdatabase!\.ref/g,
    replacement: 'database().ref'
  },
  // Fix functions!.httpsCallable -> functions().httpsCallable (with exclamation mark)
  {
    pattern: /\bfunctions!\.httpsCallable/g,
    replacement: 'functions().httpsCallable'
  },
  // Fix messaging!.getToken -> messaging().getToken (with exclamation mark)
  {
    pattern: /\bmessaging!\.getToken/g,
    replacement: 'messaging().getToken'
  },
  // Fix auth!.onAuthStateChanged -> auth().onAuthStateChanged (with exclamation mark)
  {
    pattern: /\bauth!\.onAuthStateChanged/g,
    replacement: 'auth().onAuthStateChanged'
  },
  // Fix auth!.signInWithEmailAndPassword -> auth().signInWithEmailAndPassword (with exclamation mark)
  {
    pattern: /\bauth!\.signInWithEmailAndPassword/g,
    replacement: 'auth().signInWithEmailAndPassword'
  },
  // Fix auth!.createUserWithEmailAndPassword -> auth().createUserWithEmailAndPassword (with exclamation mark)
  {
    pattern: /\bauth!\.createUserWithEmailAndPassword/g,
    replacement: 'auth().createUserWithEmailAndPassword'
  },
  // Fix auth!.signOut -> auth().signOut (with exclamation mark)
  {
    pattern: /\bauth!\.signOut/g,
    replacement: 'auth().signOut'
  },
  // Fix auth!.sendPasswordResetEmail -> auth().sendPasswordResetEmail (with exclamation mark)
  {
    pattern: /\bauth!\.sendPasswordResetEmail/g,
    replacement: 'auth().sendPasswordResetEmail'
  },
  // Fix firestore!.doc -> firestore().doc (with exclamation mark)
  {
    pattern: /\bfirestore!\.doc/g,
    replacement: 'firestore().doc'
  },
  // Fix firestore!.batch -> firestore().batch (with exclamation mark)
  {
    pattern: /\bfirestore!\.batch/g,
    replacement: 'firestore().batch'
  },
  // Fix storage!.refFromURL -> storage().refFromURL (with exclamation mark)
  {
    pattern: /\bstorage!\.refFromURL/g,
    replacement: 'storage().refFromURL'
  }
];

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { processed: false, changes: [] };
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const changes = [];

  FIREBASE_FIXES.forEach(fix => {
    if (content.match(fix.pattern)) {
      content = content.replace(fix.pattern, fix.replacement);
      hasChanges = true;
      changes.push(`Fixed: ${fix.pattern} -> ${fix.replacement}`);
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
  }

  return { processed: hasChanges, changes };
}

function processDirectory(dirPath) {
  const files = getAllFiles(dirPath);
  let totalProcessed = 0;

  files.forEach(filePath => {
    const result = processFile(filePath);
    if (result.processed) {
      console.log(`✅ ${filePath}`);
      result.changes.forEach(change => {
        console.log(`   - ${change}`);
      });
      totalProcessed++;
    }
  });

  return totalProcessed;
}

// Main execution
console.log('🔧 Fixing remaining Firebase issues...\n');
console.log('This will fix Firebase service method calls with exclamation marks:\n');
console.log('  auth!.currentUser → auth().currentUser');
console.log('  firestore!.collection → firestore().collection');
console.log('  storage!.ref → storage().ref');
console.log('  etc.\n');

let totalProcessed = 0;
for (const dir of DIRECTORIES) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    totalProcessed += processDirectory(dir);
  }
}

console.log(`\n🎉 Remaining Firebase fixes complete!`);
console.log(`📊 Total files processed: ${totalProcessed}`);
